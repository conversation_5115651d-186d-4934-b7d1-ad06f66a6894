services:
  db:
    hostname: praja-mysql
    container_name: "praja-mysql"
    image: mysql:8.0
    ports:
      - "3906:3306"
    volumes:
      - "praja-mysql:/var/lib/mysql"
    command: mysqld --sql_mode=''
    environment:
      - MYSQL_ROOT_PASSWORD=toor
      - MYSQL_USER=admin
      - MYSQL_PASSWORD=toor
      - MYSQL_DATABASE=circle_api_development
    networks:
      - praja-network

  redis:
    hostname: "praja-redis"
    container_name: "praja-redis"
    image: redis:7.0
    command: redis-server
    ports:
      - "6379:6379"
    volumes:
      - "praja-redis:/data_10"
    networks:
      - praja-network

  opensearch:
    hostname: praja-opensearch
    container_name: "praja-opensearch"
    image: opensearchproject/opensearch:2.7.0
    environment:
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
      - "DISABLE_SECURITY_PLUGIN=true"
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536 # maximum number of open files for the OpenSearch user, set to at least 65536 on modern systems
        hard: 65536
    ports:
      - "9200:9200"
      - "9600:9600"
    volumes:
      - "praja-opensearch:/usr/share/opensearch/data"
      - "./config/docker/opensearch.yml:/usr/share/opensearch/config/opensearch.yml"
    networks:
      - praja-network

  api:
    restart: always
    stdin_open: true
    tty: true
    container_name: "praja-api"
    depends_on:
      - "db"
      - "redis"
      - "opensearch"
    build:
      context: .
      dockerfile: Dockerfile
      args:
        GITHUB_SHA: dev
        RAILS_ENV: development
    ports:
      - "3000:3000"
    environment:
      SERVICE: "api"
      PORT: "3000"
      DB_USERNAME: admin
      DB_PASSWORD: toor
      DB_PORT: 3306
      DB_HOST: praja-mysql
      RAILS_ENV: development
      RAILS_MAX_THREADS: 5
      WEB_CONCURRENCY: 2
      REDIS_HOST: praja-redis
      REDIS_PORT: 6379
      ES_HOST: praja-opensearch
      ES_SCHEME: http
      ES_PORT: 9200
      GOOGLE_CLOUD_CREDENTIALS: ./config/credentials/development_google_cloud_credentials.json
      DEPLOYMENT: "development"
    volumes:
      - ".:/var/www/app"
      - "$HOME/.ssh:/root/.ssh"
      - "praja-bundle:/usr/local/bundle"
    entrypoint:
      [
        "sh",
        "-c",
        "(bundle check || bundle install) && ./config/docker/entrypoint.sh"
      ]
    networks:
      - praja-network

  sidekiq:
    restart: always
    container_name: "praja-sidekiq"
    depends_on:
      - "db"
      - "redis"
      - "opensearch"
      - "api"
    build:
      context: .
      dockerfile: Dockerfile
      args:
        GITHUB_SHA: dev
        RAILS_ENV: development
    volumes:
      - ".:/var/www/app"
      - "praja-bundle:/usr/local/bundle"
    environment:
      SERVICE: "sidekiq"
      DB_USERNAME: admin
      DB_PASSWORD: toor
      DB_PREFIX: circle_api
      DB_PORT: 3306
      DB_HOST: praja-mysql
      RAILS_ENV: development
      REDIS_HOST: praja-redis
      REDIS_PORT: 6379
      ES_HOST: praja-opensearch
      ES_SCHEME: http
      ES_PORT: 9200
      GOOGLE_CLOUD_CREDENTIALS: ./config/credentials/development_google_cloud_credentials.json
    networks:
      - praja-network

  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:2.7.0
    container_name: opensearch-dashboards
    depends_on:
      - "opensearch"
    ports:
      - 5601:5601
    expose:
      - "5601"
    environment:
      DISABLE_SECURITY_DASHBOARDS_PLUGIN: "true"
      OPENSEARCH_HOSTS: '["http://praja-opensearch:9200"]'
    networks:
      - praja-network

volumes:
  praja-redis:
    external: true
  praja-mysql:
    external: true
  praja-opensearch:
    external: true
  praja-bundle:
    external: true

networks:
  praja-network:
    name: praja-network
    external: true
    driver: host
