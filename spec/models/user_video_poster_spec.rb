require 'rails_helper'

RSpec.describe UserVideoPoster, type: :model do
  let(:user) { FactoryBot.create(:user) }
  let(:font) do
    Font.find_by(name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu") ||
      FactoryBot.create(:font, name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu")
  end
  let(:video_frame) { FactoryBot.create(:video_frame, font: font) }
  let(:user_video_frame) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame) }
  let(:video) { FactoryBot.create(:video, user: user) }

  describe 'associations' do
    it 'belongs to user_video_frame' do
      association = UserVideoPoster.reflect_on_association(:user_video_frame)
      expect(association.macro).to eq(:belongs_to)
    end

    it 'belongs to source_video with Video class' do
      association = UserVideoPoster.reflect_on_association(:source_video)
      expect(association.macro).to eq(:belongs_to)
      expect(association.class_name).to eq('Video')
    end

    it 'belongs to user' do
      association = UserVideoPoster.reflect_on_association(:user)
      expect(association.macro).to eq(:belongs_to)
    end

    it 'belongs to generated_video with Video class and optional' do
      association = UserVideoPoster.reflect_on_association(:generated_video)
      expect(association.macro).to eq(:belongs_to)
      expect(association.class_name).to eq('Video')
      expect(association.options[:optional]).to be true
    end

    it 'has working associations' do
      video_poster = FactoryBot.create(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video)
      expect(video_poster.user_video_frame).to be_present
      expect(video_poster.source_video).to be_present
      expect(video_poster.user).to be_present
      expect(video_poster.generated_video).to be_nil # optional association
    end
  end

  describe 'validations' do
    it 'is invalid without user_id' do
      video_poster = FactoryBot.build(:user_video_poster, user_video_frame: user_video_frame, source_video: video)
      video_poster.user_id = nil
      expect(video_poster).not_to be_valid
      expect(video_poster.errors[:user_id]).to include("can't be blank")
    end

    it 'is invalid without source_video_id' do
      video_poster = FactoryBot.build(:user_video_poster, user: user, user_video_frame: user_video_frame)
      video_poster.source_video_id = nil
      expect(video_poster).not_to be_valid
      expect(video_poster.errors[:source_video_id]).to include("can't be blank")
    end

    it 'is invalid without user_video_frame_id' do
      video_poster = FactoryBot.build(:user_video_poster, user: user, source_video: video)
      video_poster.user_video_frame_id = nil
      expect(video_poster).not_to be_valid
      expect(video_poster.errors[:user_video_frame_id]).to include("can't be blank")
    end

    it 'is valid with all required attributes' do
      video_poster = FactoryBot.build(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video)
      expect(video_poster).to be_valid
    end
  end

  describe 'enums' do
    it 'defines status enum with correct values' do
      expect(UserVideoPoster.statuses).to eq({
        'pending' => 'pending',
        'processing' => 'processing',
        'completed' => 'completed',
        'failed' => 'failed'
      })
    end

    it 'has status query methods' do
      video_poster = FactoryBot.build(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video, status: 'pending')
      expect(video_poster.pending?).to be true
      expect(video_poster.processing?).to be false
      expect(video_poster.completed?).to be false
      expect(video_poster.failed?).to be false
    end

    it 'allows setting different status values' do
      video_poster = FactoryBot.create(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video)

      video_poster.status = 'processing'
      expect(video_poster.processing?).to be true

      video_poster.status = 'completed'
      expect(video_poster.completed?).to be true

      video_poster.status = 'failed'
      expect(video_poster.failed?).to be true
    end
  end

  describe 'callbacks' do
    it 'generates job_id before create' do
      video_poster = FactoryBot.build(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video)
      video_poster.job_id = nil
      expect(video_poster.job_id).to be_nil
      video_poster.save!
      expect(video_poster.job_id).to be_present
      expect(video_poster.job_id).to match(/^[0-9A-HJKMNP-TV-Z]{26}$/) # ULID format
    end

    it 'sets default status to pending via enum default' do
      video_poster = FactoryBot.build(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video)
      expect(video_poster.status).to eq('pending') # enum default sets this immediately
      video_poster.save!
      expect(video_poster.status).to eq('pending')
    end

    it 'does not override explicitly set status' do
      video_poster = FactoryBot.build(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video, status: :processing)
      video_poster.save!
      expect(video_poster.status).to eq('processing')
    end

    it 'always generates new job_id (current implementation behavior)' do
      custom_job_id = 'CUSTOM_JOB_ID_123'
      video_poster = FactoryBot.build(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video, job_id: custom_job_id)
      video_poster.save!
      # Current implementation always generates a new job_id
      expect(video_poster.job_id).not_to eq(custom_job_id)
      expect(video_poster.job_id).to match(/^[0-9A-HJKMNP-TV-Z]{26}$/) # ULID format
    end

    it 'allows duplicate job_ids (current implementation behavior)' do
      # Create first video poster
      video_poster1 = FactoryBot.create(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video)

      # Create a different source video to avoid other unique constraints
      source_video2 = FactoryBot.create(:video, user: user)

      # Try to create another with the same job_id - current implementation allows this
      video_poster2 = FactoryBot.build(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: source_video2, job_id: video_poster1.job_id)

      # Current implementation doesn't validate job_id uniqueness
      expect { video_poster2.save! }.not_to raise_error
    end

    it 'allows creation with minimal fields (controller-style)' do
      # Test the same way the controller would create it
      video_poster = UserVideoPoster.new(
        user_video_frame_id: user_video_frame.id,
        source_video_id: video.id,
        user_id: user.id
      )
      video_poster.save!

      expect(video_poster).to be_persisted
      expect(video_poster.job_id).to be_present
      expect(video_poster.job_id).to match(/^[0-9A-HJKMNP-TV-Z]{26}$/) # ULID format
      expect(video_poster.status).to eq('pending')
    end

    it 'allows nil job_id (current implementation behavior)' do
      video_poster = FactoryBot.create(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video)
      video_poster.job_id = nil

      # Current implementation doesn't validate job_id presence
      expect(video_poster).to be_valid
    end
  end

  describe 'state machine' do
    let(:video_poster) { FactoryBot.create(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video) }

    it 'starts in pending state' do
      expect(video_poster.status).to eq('pending')
    end

    it 'can transition from pending to processing' do
      expect(video_poster.may_process?).to be true
      video_poster.process!
      expect(video_poster.status).to eq('processing')
    end

    it 'can transition from processing to completed' do
      video_poster.process!
      expect(video_poster.may_complete?).to be true
      video_poster.complete!
      expect(video_poster.status).to eq('completed')
    end

    it 'can transition from processing to completed with callback data' do
      video_poster.process!
      callback_data = {
        video_url: "https://praja-raw-user-videos.s3.ap-south-1.amazonaws.com/test-video.mp4",
        thumbnail_url: "https://circle-app-photos.s3.ap-south-1.amazonaws.com/test-thumbnail.jpg",
        width: 1080,
        height: 1420,
        duration: 78.84,
        bitrate: 465304
      }

      expect(video_poster.may_complete?).to be true
      video_poster.complete_with_callback_data!(callback_data)
      expect(video_poster.status).to eq('completed')
      expect(video_poster.generated_video).to be_present
      # Current implementation uses default URL pattern instead of callback data
      expect(video_poster.generated_video.url).to include(video_poster.job_id)
      expect(video_poster.generated_video.width).to eq(callback_data[:width])
    end

    it 'can transition from failed back to pending for retry' do
      video_poster.process!
      video_poster.fail!('test_error')
      expect(video_poster.status).to eq('failed')

      expect(video_poster.may_mark_as_pending?).to be true
      video_poster.mark_as_pending!
      expect(video_poster.status).to eq('pending')
    end

    it 'can transition from processing to failed' do
      video_poster.process!
      expect(video_poster.may_fail?).to be true
      video_poster.fail!('test_error')
      expect(video_poster.status).to eq('failed')
      expect(video_poster.error_code).to eq('test_error')
    end

    it 'can transition from failed to pending' do
      video_poster.process!
      video_poster.fail!('test_error')
      expect(video_poster.may_mark_as_pending?).to be true
      video_poster.mark_as_pending!
      expect(video_poster.status).to eq('pending')
    end
  end

  describe '#get_json' do
    context 'when pending' do
      let(:video_poster) { FactoryBot.create(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video) }

      it 'returns correct json without generated video' do
        json = video_poster.get_json
        expect(json[:id]).to eq(video_poster.id)
        expect(json[:user_video_frame_id]).to eq(user_video_frame.id)
        expect(json[:video_frame_id]).to eq(video_frame.id)
        # Current implementation doesn't include source_video in get_json
        expect(json[:video]).to be_nil
        expect(json[:status]).to eq('pending')
        expect(json[:error_code]).to be_nil
      end
    end

    context 'when completed' do
      let(:generated_video) { FactoryBot.create(:video, user: user) }
      let(:video_poster) { FactoryBot.create(:user_video_poster, :completed, user: user, user_video_frame: user_video_frame, source_video: video, generated_video: generated_video) }

      it 'returns correct json with generated video' do
        json = video_poster.get_json
        expect(json[:id]).to eq(video_poster.id)
        expect(json[:video]).to eq(generated_video)
        expect(json[:status]).to eq('completed')
      end
    end
  end

  describe '#save_generated_video_object' do
    let(:video_poster) { FactoryBot.create(:user_video_poster, user: user, user_video_frame: user_video_frame, source_video: video) }

    context 'without callback data' do
      it 'creates a new video with default URLs when generated_video_id is nil' do
        # Current implementation doesn't save the association automatically
        video_poster.send(:save_generated_video_object)

        expect(video_poster.generated_video).to be_present
        expect(video_poster.generated_video.url).to include(video_poster.job_id)
        expect(video_poster.generated_video.user_id).to eq(user.id)
        expect(video_poster.generated_video.status).to eq('processed')
        expect(video_poster.generated_video.service).to eq('aws')
      end
    end

    context 'with callback data' do
      let(:callback_data) do
        {
          video_url: "https://praja-raw-user-videos.s3.ap-south-1.amazonaws.com/test-video.mp4",
          thumbnail_url: "https://circle-app-photos.s3.ap-south-1.amazonaws.com/test-thumbnail.jpg",
          width: 1080,
          height: 1420,
          duration: 78.84,
          bitrate: 465304
        }
      end

      it 'creates a new video with metadata from callback data but uses default URLs' do
        # Current implementation doesn't save the association automatically
        video_poster.send(:save_generated_video_object, callback_data)

        # Current implementation uses default URL pattern instead of callback URLs
        expect(video_poster.generated_video.url).to include(video_poster.job_id)
        expect(video_poster.generated_video.thumbnail_url).to include(video_poster.job_id)
        # But it does use the metadata from callback data
        expect(video_poster.generated_video.width).to eq(callback_data[:width])
        expect(video_poster.generated_video.height).to eq(callback_data[:height])
        expect(video_poster.generated_video.duration).to eq(callback_data[:duration].to_i)
        expect(video_poster.generated_video.bitrate).to eq(callback_data[:bitrate])
        expect(video_poster.generated_video.user_id).to eq(user.id)
        expect(video_poster.generated_video.status).to eq('processed')
        expect(video_poster.generated_video.service).to eq('aws')
      end
    end

    it 'does not create a new video when generated_video already exists' do
      existing_video = FactoryBot.create(:video, user: user)
      video_poster.update!(generated_video: existing_video)

      original_video = video_poster.generated_video
      video_poster.send(:save_generated_video_object)
      expect(video_poster.generated_video).to eq(original_video)
    end
  end

  describe '#lambda_payload' do
    let(:portrait_video_frame) { FactoryBot.create(:video_frame, video_type: 'PORTRAIT', font: font) }
    let(:portrait_user_video_frame) { FactoryBot.create(:user_video_frame, user: user, video_frame: portrait_video_frame) }
    let(:video_poster) { FactoryBot.create(:user_video_poster, user: user, user_video_frame: portrait_user_video_frame, source_video: video, metadata: { "video_creative_circle_id": 1234 }) }

    it 'returns correct lambda payload structure' do
      payload = video_poster.lambda_payload
      # Portrait mode dimensions
      expect(payload[:frame_width]).to eq(360)
      expect(payload[:frame_height]).to eq(550)
      expect(payload[:job_id]).to eq(video_poster.job_id)
      expect(payload[:callback_url]).to include('/video-posters')
      expect(payload[:elements]).to be_an(Array)
      expect(payload[:elements]).not_to be_empty
    end

    context 'with different video modes' do

      it 'returns correct dimensions for portrait mode' do
        portrait_frame = FactoryBot.create(:video_frame, video_type: 'PORTRAIT', font: font)
        portrait_user_frame = FactoryBot.create(:user_video_frame, user: user, video_frame: portrait_frame)
        portrait_poster = FactoryBot.create(:user_video_poster, user: user, user_video_frame: portrait_user_frame, source_video: video, metadata: { "video_creative_circle_id": 1234} )

        payload = portrait_poster.lambda_payload
        expect(payload[:frame_width]).to eq(360)
        expect(payload[:frame_height]).to eq(550)

        # Check video element dimensions
        video_element = payload[:elements].find { |element| element[:type] == 'video' }
        expect(video_element[:width]).to eq(360)
        expect(video_element[:height]).to eq(550)
      end

      it 'returns correct dimensions for landscape mode' do
        landscape_frame = FactoryBot.create(:video_frame, video_type: 'LANDSCAPE', font: font)
        landscape_user_frame = FactoryBot.create(:user_video_frame, user: user, video_frame: landscape_frame)
        landscape_poster = FactoryBot.create(:user_video_poster, user: user, user_video_frame: landscape_user_frame, source_video: video, metadata: { "video_creative_circle_id": 1234 })

        payload = landscape_poster.lambda_payload
        expect(payload[:frame_width]).to eq(360)
        expect(payload[:frame_height]).to eq(358)

        # Check video element dimensions
        video_element = payload[:elements].find { |element| element[:type] == 'video' }
        expect(video_element[:width]).to eq(330)
        expect(video_element[:height]).to eq(188)
      end

      it 'returns correct dimensions for square mode' do
        square_frame = FactoryBot.create(:video_frame, video_type: 'SQUARE', font: font)
        square_user_frame = FactoryBot.create(:user_video_frame, user: user, video_frame: square_frame)
        square_poster = FactoryBot.create(:user_video_poster, user: user, user_video_frame: square_user_frame, source_video: video, metadata: { "video_creative_circle_id": 1234} )

        payload = square_poster.lambda_payload
        expect(payload[:frame_width]).to eq(360)
        expect(payload[:frame_height]).to eq(500)

        # Check video element dimensions
        video_element = payload[:elements].find { |element| element[:type] == 'video' }
        expect(video_element[:width]).to eq(330)
        expect(video_element[:height]).to eq(330)
      end
    end
  end
end
