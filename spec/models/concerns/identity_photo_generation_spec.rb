# frozen_string_literal: true

require 'rails_helper'

# Test class to include the concern
class TestIdentityPhotoGeneration
  include IdentityPhotoGeneration
end

RSpec.describe IdentityPhotoGeneration, type: :concern do
  let(:test_class) { TestIdentityPhotoGeneration.new }
  let(:user) { FactoryBot.create(:user) }
  let(:font) { Font.find_by(name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu") || FactoryBot.create(:font, name_font: "Noto Sans Telugu #{SecureRandom.hex(4)}", badge_font: "Noto Sans Telugu #{SecureRandom.hex(4)}") }
  let(:video_frame) { FactoryBot.create(:video_frame, video_type: 'PORTRAIT', font: font, active: true) }

  before do
    # Mock the Capture module to avoid actual image generation in tests
    allow(Capture).to receive(:capture_html_as_image).and_return({ 'cdn_url' => 'https://example.com/identity.jpg' })
  end

  describe '#generate_identity_image' do
    context 'when capture succeeds' do
      it 'returns the captured URL' do
        expect(test_class.send(:generate_identity_image, user, video_frame)).to eq('https://example.com/identity.jpg')
      end

      it 'calls capture with correct parameters' do
        expect(Capture).to receive(:capture_html_as_image).with(kind_of(String), '#top-outer-container', false)
        test_class.send(:generate_identity_image, user, video_frame)
      end

      it 'generates HTML with user and video frame data' do
        expect(test_class).to receive(:generate_html).with(
          template_name: 'identity_photo_template',
          video_type: video_frame.video_type,
          user_name: user.name,
          badge_description: user.get_badge_role_including_unverified&.get_json&.dig('description'),
          name_font_family: font.name_font,
          badge_font_family: font.badge_font
        ).and_return('<html>test</html>')

        test_class.send(:generate_identity_image, user, video_frame)
      end
    end

    context 'when capture fails' do
      before do
        allow(Capture).to receive(:capture_html_as_image).and_return({ 'cdn_url' => nil })
      end

      it 'raises an error' do
        expect {
          test_class.send(:generate_identity_image, user, video_frame)
        }.to raise_error(RuntimeError, /Did not receive url from captured html/)
      end
    end

    context 'when capture returns empty response' do
      before do
        allow(Capture).to receive(:capture_html_as_image).and_return({})
      end

      it 'raises an error' do
        expect {
          test_class.send(:generate_identity_image, user, video_frame)
        }.to raise_error(RuntimeError, /Did not receive url from captured html/)
      end
    end
  end

  describe '#generate_html' do
    let(:template_params) do
      {
        template_name: 'identity_photo_template',
        video_type: 'PORTRAIT',
        user_name: 'Test User',
        badge_description: 'Test Badge',
        name_font_family: 'Arial',
        badge_font_family: 'Helvetica'
      }
    end

    before do
      allow(ActionController::Base).to receive(:render).and_return('<html>rendered</html>')
      allow(File).to receive(:read).and_return('<%= user_name %>')
    end

    it 'renders the template with correct locals' do
      expect(ActionController::Base).to receive(:render).with(
        inline: '<%= user_name %>',
        locals: hash_including(
          user_name: 'Test User',
          badge_description: 'Test Badge',
          name_font_family: 'Arial',
          badge_font_family: 'Helvetica'
        )
      )

      test_class.send(:generate_html, **template_params)
    end

    it 'calculates font size based on name length and video type' do
      expect(test_class).to receive(:calculate_name_font_size_with_video_type).with('Test User', 'PORTRAIT').and_return(24)

      test_class.send(:generate_html, **template_params)
    end

    it 'sets user photo width to 0 for PORTRAIT video type' do
      expect(ActionController::Base).to receive(:render).with(
        inline: anything,
        locals: hash_including(user_photo_width: 0)
      )

      test_class.send(:generate_html, **template_params.merge(video_type: 'PORTRAIT'))
    end

    it 'sets user photo width correctly for LANDSCAPE video type' do
      user_photo_dimensions = UserVideoFrame::USER_PHOTO_DIMENSIONS['LANDSCAPE']
      expected_width = user_photo_dimensions[:width] + 70

      expect(ActionController::Base).to receive(:render).with(
        inline: anything,
        locals: hash_including(user_photo_width: expected_width)
      )

      test_class.send(:generate_html, **template_params.merge(video_type: 'LANDSCAPE'))
    end
  end

  describe '#calculate_name_font_size_with_video_type' do
    context 'with different name lengths and video types' do
      context 'for PORTRAIT video type' do
        it 'returns correct font size for short names (0-38 chars)' do
          expect(test_class.send(:calculate_name_font_size_with_video_type, 'John', 'PORTRAIT')).to eq(38)
        end

        it 'returns correct font size for medium length names (38-50 chars)' do
          name = 'A' * 40
          expect(test_class.send(:calculate_name_font_size_with_video_type, name, 'PORTRAIT')).to eq(32)
        end

        it 'returns correct font size for long names (50-60 chars)' do
          name = 'A' * 55
          expect(test_class.send(:calculate_name_font_size_with_video_type, name, 'PORTRAIT')).to eq(28)
        end

        it 'returns minimum font size for very long names (60+ chars)' do
          name = 'A' * 65
          expect(test_class.send(:calculate_name_font_size_with_video_type, name, 'PORTRAIT')).to eq(26)
        end

        it 'handles empty names' do
          expect(test_class.send(:calculate_name_font_size_with_video_type, '', 'PORTRAIT')).to eq(38)
        end

        it 'handles boundary cases' do
          # Test exact boundary at 38 characters
          name_38 = 'A' * 38
          expect(test_class.send(:calculate_name_font_size_with_video_type, name_38, 'PORTRAIT')).to eq(38)

          # Test exact boundary at 50 characters
          name_50 = 'A' * 50
          expect(test_class.send(:calculate_name_font_size_with_video_type, name_50, 'PORTRAIT')).to eq(32)

          # Test exact boundary at 60 characters
          name_60 = 'A' * 60
          expect(test_class.send(:calculate_name_font_size_with_video_type, name_60, 'PORTRAIT')).to eq(28)
        end
      end

      context 'for LANDSCAPE video type' do
        it 'returns correct font size for short names (0-30 chars)' do
          expect(test_class.send(:calculate_name_font_size_with_video_type, 'John', 'LANDSCAPE')).to eq(30)
        end

        it 'returns correct font size for medium length names (30-40 chars)' do
          name = 'A' * 35
          expect(test_class.send(:calculate_name_font_size_with_video_type, name, 'LANDSCAPE')).to eq(26)
        end

        it 'returns correct font size for long names (40-50 chars)' do
          name = 'A' * 45
          expect(test_class.send(:calculate_name_font_size_with_video_type, name, 'LANDSCAPE')).to eq(22)
        end

        it 'returns correct font size for very long names (50-60 chars)' do
          name = 'A' * 55
          expect(test_class.send(:calculate_name_font_size_with_video_type, name, 'LANDSCAPE')).to eq(20)
        end

        it 'returns minimum font size for extremely long names (60+ chars)' do
          name = 'A' * 65
          expect(test_class.send(:calculate_name_font_size_with_video_type, name, 'LANDSCAPE')).to eq(18)
        end

        it 'handles empty names' do
          expect(test_class.send(:calculate_name_font_size_with_video_type, '', 'LANDSCAPE')).to eq(30)
        end

        it 'handles boundary cases' do
          # Test exact boundary at 30 characters
          name_30 = 'A' * 30
          expect(test_class.send(:calculate_name_font_size_with_video_type, name_30, 'LANDSCAPE')).to eq(30)

          # Test exact boundary at 40 characters
          name_40 = 'A' * 40
          expect(test_class.send(:calculate_name_font_size_with_video_type, name_40, 'LANDSCAPE')).to eq(26)

          # Test exact boundary at 50 characters
          name_50 = 'A' * 50
          expect(test_class.send(:calculate_name_font_size_with_video_type, name_50, 'LANDSCAPE')).to eq(22)

          # Test exact boundary at 60 characters
          name_60 = 'A' * 60
          expect(test_class.send(:calculate_name_font_size_with_video_type, name_60, 'LANDSCAPE')).to eq(20)
        end
      end

      context 'for SQUARE video type' do
        it 'returns correct font size for short names (0-30 chars)' do
          expect(test_class.send(:calculate_name_font_size_with_video_type, 'John', 'SQUARE')).to eq(30)
        end

        it 'returns correct font size for medium length names (30-40 chars)' do
          name = 'A' * 35
          expect(test_class.send(:calculate_name_font_size_with_video_type, name, 'SQUARE')).to eq(26)
        end

        it 'returns correct font size for long names (40-50 chars)' do
          name = 'A' * 45
          expect(test_class.send(:calculate_name_font_size_with_video_type, name, 'SQUARE')).to eq(22)
        end

        it 'returns correct font size for very long names (50-60 chars)' do
          name = 'A' * 55
          expect(test_class.send(:calculate_name_font_size_with_video_type, name, 'SQUARE')).to eq(20)
        end

        it 'returns minimum font size for extremely long names (60+ chars)' do
          name = 'A' * 65
          expect(test_class.send(:calculate_name_font_size_with_video_type, name, 'SQUARE')).to eq(18)
        end

        it 'handles empty names' do
          expect(test_class.send(:calculate_name_font_size_with_video_type, '', 'SQUARE')).to eq(30)
        end

        it 'handles boundary cases' do
          # Test exact boundary at 30 characters
          name_30 = 'A' * 30
          expect(test_class.send(:calculate_name_font_size_with_video_type, name_30, 'SQUARE')).to eq(30)

          # Test exact boundary at 40 characters
          name_40 = 'A' * 40
          expect(test_class.send(:calculate_name_font_size_with_video_type, name_40, 'SQUARE')).to eq(26)

          # Test exact boundary at 50 characters
          name_50 = 'A' * 50
          expect(test_class.send(:calculate_name_font_size_with_video_type, name_50, 'SQUARE')).to eq(22)

          # Test exact boundary at 60 characters
          name_60 = 'A' * 60
          expect(test_class.send(:calculate_name_font_size_with_video_type, name_60, 'SQUARE')).to eq(20)
        end
      end
    end
  end

  describe 'concern inclusion' do
    it 'includes the Capture module' do
      expect(TestIdentityPhotoGeneration.included_modules).to include(Capture)
    end

    it 'provides private methods' do
      expect(test_class.private_methods).to include(:generate_identity_image)
      expect(test_class.private_methods).to include(:generate_html)
      expect(test_class.private_methods).to include(:calculate_name_font_size_with_video_type)
    end
  end
end
