# frozen_string_literal: true

require 'rails_helper'

RSpec.describe IdentityPhotoGenerationWorker, type: :worker do
  let(:user) { FactoryBot.create(:user) }
  let(:font) { FactoryBot.create(:font, name_font: "Noto Sans Telugu #{SecureRandom.hex(4)}", badge_font: "Noto Sans Devanagari #{SecureRandom.hex(4)}") }
  let(:video_frame) { FactoryBot.create(:video_frame, id: 12, active: true, font: font) }
  let(:user_video_frame) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame, active: true) }
  let(:mock_capture) { instance_double(Capture) }

  before do
    allow(Capture).to receive(:capture_html_as_image).and_return({ 'cdn_url' => 'https://example.com/image.jpg' })
  end

  describe '#perform' do
    subject { described_class.new }

    context 'when user_id is blank' do
      it 'returns early' do
        expect(subject.perform(nil)).to be_nil
        expect(subject.perform('')).to be_nil
      end
    end

    context 'when user is not found' do
      it 'returns early' do
        expect(subject.perform(999)).to be_nil
      end
    end

    context 'when no active video frames exist' do
      before do
        VideoFrame.update_all(active: false)
      end

      it 'returns early' do
        expect(subject.perform(user.id)).to be_nil
      end
    end

    context 'when generating identity image fails' do
      before do
        # Create a user_video_frame so the worker has something to process
        user_video_frame
        allow_any_instance_of(IdentityPhotoGenerationWorker).to receive(:generate_identity_image).and_raise(StandardError.new('Capture failed'))
        allow(Rails.logger).to receive(:error)
        allow(Honeybadger).to receive(:notify)
      end

      it 'logs error and continues with other frames' do
        expect(Honeybadger).to receive(:notify).with(
          kind_of(StandardError),
          context: { user_id: user.id, video_frame_id: video_frame.id }
        )
        expect(Rails.logger).to receive(:error).with(
          "Failed to generate identity image for user #{user.id}, video_frame #{video_frame.id}: Capture failed"
        )

        subject.perform(user.id)
      end
    end

    context 'when identity image generation is successful' do
      before do
        allow_any_instance_of(IdentityPhotoGenerationWorker).to receive(:generate_identity_image).and_return('https://example.com/identity_photo.jpg')
        allow(Rails.logger).to receive(:info)
      end

      it 'updates user video frame with new identity image URL' do
        FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame, active: true, identity_photo_url: 'https://example.com/old.jpg')
        expect(Rails.logger).to receive(:info).with(
          "New identity image generated for user #{user.id}, video_frame #{video_frame.id}"
        )

        subject.perform(user.id)

        user_video_frame = UserVideoFrame.find_by(user_id: user.id, video_frame_id: video_frame.id)
        expect(user_video_frame.identity_photo_url).to eq('https://example.com/identity_photo.jpg')
      end

      it 'deactivates all user video posters' do
        # Create a user_video_frame so the worker has something to process
        FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame, active: true, identity_photo_url: 'https://example.com/existing.jpg')

        # Create a simple user_video_poster without complex dependencies
        user_video_poster = UserVideoPoster.create!(
          user: user,
          user_video_frame: FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame, active: true, identity_photo_url: 'https://example.com/test.jpg'),
          source_video: FactoryBot.create(:video),
          status: 'pending',
          job_id: SecureRandom.uuid,
          active: true
        )

        allow(Rails.logger).to receive(:info)
        subject.perform(user.id)
        user_video_poster.reload
        expect(user_video_poster.active).to be false
      end
    end

    context 'when an error occurs during processing' do
      before do
        allow(VideoFrame).to receive(:where).and_raise(StandardError.new('Database error'))
        allow(Rails.logger).to receive(:error)
        allow(Honeybadger).to receive(:notify)
      end

      it 'notifies Honeybadger and raises the error' do
        expect(Honeybadger).to receive(:notify).with(
          kind_of(StandardError),
          context: { user_id: user.id }
        )
        expect(Rails.logger).to receive(:error).with(
          "IdentityImagesWorker failed: Database error"
        )

        expect { subject.perform(user.id) }.to raise_error(StandardError, 'Database error')
      end
    end
  end

  describe 'sidekiq configuration' do
    it 'uses video_posters_generation queue' do
      expect(described_class.sidekiq_options['queue']).to eq(:video_posters_generation)
    end

    it 'has 3 retry attempts' do
      expect(described_class.sidekiq_options['retry']).to eq(3)
    end

    it 'has lock until and while executing for deduplication' do
      expect(described_class.sidekiq_options['lock']).to eq(:until_and_while_executing)
    end

    it 'logs conflicts when duplicate jobs are attempted' do
      expect(described_class.sidekiq_options['on_conflict']).to eq(:log)
    end
  end

  describe 'retries exhausted callback' do
    it 'notifies Honeybadger when retries are exhausted' do
      msg = { 'args' => [user.id] }
      ex = StandardError.new('Test error')

      expect(Honeybadger).to receive(:notify).with(ex, context: { args: [user.id] })
      expect(Rails.logger).to receive(:error).with(
        'IdentityPhotoGenerationWorker retries exhausted: Test error'
      )

      described_class.sidekiq_retries_exhausted_block.call(msg, ex)
    end
  end

  describe '#generate_identity_image' do
    subject { described_class.new }

    context 'when capture fails' do
      before do
        allow(Capture).to receive(:capture_html_as_image).and_return({ 'cdn_url' => nil })
      end

      it 'raises an error' do
        expect {
          subject.send(:generate_identity_image, user, video_frame)
        }.to raise_error(RuntimeError, /Did not receive url from captured html/)
      end
    end

    context 'when capture succeeds' do
      let(:captured_url) { 'https://example.com/captured.jpg' }

      before do
        allow(Capture).to receive(:capture_html_as_image).and_return({ 'cdn_url' => captured_url })
      end

      it 'returns the captured URL' do
        expect(subject.send(:generate_identity_image, user, video_frame)).to eq(captured_url)
      end
    end
  end

  describe '#calculate_name_font_size_with_video_type' do
    subject { described_class.new }

    context 'with different name lengths and video types' do
      context 'for PORTRAIT video type' do
        it 'returns correct font size for short names (0-38 chars)' do
          expect(subject.send(:calculate_name_font_size_with_video_type, 'John', 'PORTRAIT')).to eq(42)
        end

        it 'returns correct font size for medium length names (38-45 chars)' do
          name = 'A' * 40
          expect(subject.send(:calculate_name_font_size_with_video_type, name, 'PORTRAIT')).to eq(36)
        end

        it 'returns minimum font size for long names (45+ chars)' do
          name = 'A' * 50
          expect(subject.send(:calculate_name_font_size_with_video_type, name, 'PORTRAIT')).to eq(26)
        end

        it 'handles boundary cases' do
          # Test exact boundary at 38 characters
          name_38 = 'A' * 38
          expect(subject.send(:calculate_name_font_size_with_video_type, name_38, 'PORTRAIT')).to eq(42)

          # Test exact boundary at 45 characters
          name_45 = 'A' * 45
          expect(subject.send(:calculate_name_font_size_with_video_type, name_45, 'PORTRAIT')).to eq(36)

          # Test long name
          name_50 = 'A' * 50
          expect(subject.send(:calculate_name_font_size_with_video_type, name_50, 'PORTRAIT')).to eq(26)
        end
      end

      context 'for LANDSCAPE video type' do
        it 'returns correct font size for short names (0-38 chars)' do
          expect(subject.send(:calculate_name_font_size_with_video_type, 'John', 'LANDSCAPE')).to eq(36)
        end

        it 'returns correct font size for medium length names (38-45 chars)' do
          name = 'A' * 40
          expect(subject.send(:calculate_name_font_size_with_video_type, name, 'LANDSCAPE')).to eq(26)
        end

        it 'returns minimum font size for long names (45+ chars)' do
          name = 'A' * 50
          expect(subject.send(:calculate_name_font_size_with_video_type, name, 'LANDSCAPE')).to eq(22)
        end

        it 'handles boundary cases' do
          # Test exact boundary at 38 characters
          name_38 = 'A' * 38
          expect(subject.send(:calculate_name_font_size_with_video_type, name_38, 'LANDSCAPE')).to eq(36)

          # Test exact boundary at 45 characters
          name_45 = 'A' * 45
          expect(subject.send(:calculate_name_font_size_with_video_type, name_45, 'LANDSCAPE')).to eq(26)

          # Test long name
          name_50 = 'A' * 50
          expect(subject.send(:calculate_name_font_size_with_video_type, name_50, 'LANDSCAPE')).to eq(22)
        end
      end

      context 'for SQUARE video type' do
        it 'returns correct font size for short names (0-38 chars)' do
          expect(subject.send(:calculate_name_font_size_with_video_type, 'John', 'SQUARE')).to eq(36)
        end

        it 'returns correct font size for medium length names (38-45 chars)' do
          name = 'A' * 40
          expect(subject.send(:calculate_name_font_size_with_video_type, name, 'SQUARE')).to eq(26)
        end

        it 'returns minimum font size for long names (45+ chars)' do
          name = 'A' * 50
          expect(subject.send(:calculate_name_font_size_with_video_type, name, 'SQUARE')).to eq(22)
        end

        it 'handles boundary cases' do
          # Test exact boundary at 38 characters
          name_38 = 'A' * 38
          expect(subject.send(:calculate_name_font_size_with_video_type, name_38, 'SQUARE')).to eq(36)

          # Test exact boundary at 45 characters
          name_45 = 'A' * 45
          expect(subject.send(:calculate_name_font_size_with_video_type, name_45, 'SQUARE')).to eq(26)

          # Test long name
          name_50 = 'A' * 50
          expect(subject.send(:calculate_name_font_size_with_video_type, name_50, 'SQUARE')).to eq(22)
        end
      end
    end
  end
end 
