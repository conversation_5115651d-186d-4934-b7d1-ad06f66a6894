ActiveAdmin.register Circle do
  menu parent: 'Circles'
  permit_params :name, :name_en, :short_name, :circle_type, :level, :parent_circle_id, :active, :photo, :short_info,
                :banner, :leader_photo_1, :leader_photo_2, :website_url, :conversation_type, :circle_package_id,
                :start_date, :end_date, :relationship_manager_id, :entity_type, :entity_id, :slogan_icon,
                poster_photos_attributes: [:id, :photo, :_destroy]

  searchable_select_options(name: :villages,
                            # get all circles whose level is village, municipality, corporation
                            scope: -> { Circle.where(active: true, level: Constants.village_levels) },
                            text_attribute: :name_en,
                            display_text: ->(record) {
                              "#{record.name_en}, #{record.parent_circle.name_en}, #{record.parent_circle.parent_circle.name_en}"
                            },
                            filter: lambda do |term, scope|
                              scope.ransack(m: 'or', id_eq: term, name_en: term).result
                            end
  )

  searchable_select_options(name: :post_circles,
                            scope: -> {
                              Circle.where(active: true, level: Constants.village_levels)
                                    .or(Circle.where(active: true, circle_type: :interest))
                                    .or(Circle.where(active: true, circle_type: :my_circle, level: :private))
                                    .order(id: :asc)
                            },
                            text_attribute: :name_en,
                            display_text: ->(record) {
                              if record.level.to_sym.in?(Constants.village_levels)
                                "#{record.name_en}, #{record.parent_circle.name_en}, #{record.parent_circle.parent_circle.name_en}"
                              else
                                "#{record.name_en}"
                              end
                            })
  searchable_select_options(name: :sub_circle_filter_location_circles,
                            scope: -> {
                              Circle.where(active: true, circle_type: :location).order(id: :asc)
                            },
                            text_attribute: :name_en,
                            display_text: ->(record) {
                              record.searchable_select_name_for_sub_circles
                            })

  searchable_select_options(
    name: :poster_circles,
    scope: -> {
      Circle.where(active: true, level: [:political_party, :political_leader, :village, :mandal, :district, :state,
                                         :municipality, :corporation])
            .or(Circle.where(id: 0))
            .or(Circle.where(active: true, circle_type: :my_circle, level: :private))
    },
    display_text: ->(record) do
      "#{record.id}, #{record.name}, #{record.level}"
    end,
    text_attribute: :name_en,
    filter: lambda do |term, scope|
      scope.ransack(m: 'or', id_start: term, name_en_cont: term, short_name_cont: term).result
    end
  )

  searchable_select_options(
    name: :party_circles,
    scope: -> {
      Circle.where(active: true, level: [:political_party])
    },
    display_text: ->(record) do
      "#{record.id}, #{record.name}, #{record.level}"
    end,
    text_attribute: :name_en,
    filter: lambda do |term, scope|
      scope.ransack(m: 'or', id_start: term, name_en_cont: term, short_name_cont: term).result
    end
  )

  searchable_select_options(
    name: :user_role_supported_parent_circles,
    scope: -> {
      Circle.where(active: true, circle_type: [:interest, :business])
            .where.not(level: [:public_figure, :others])
            .order(Arel.sql("CASE WHEN level = 6 THEN 1 WHEN level = 7 THEN 2 ELSE 3 END"))
    },
    display_text: ->(record) do
      "#{record.id}, #{record.name}, #{record.level}"
    end,
    text_attribute: :name_en,
    filter: lambda do |term, scope|
      scope.ransack(m: 'or', id_start: term, name_en_cont: term, short_name_cont: term).result
    end
  )

  searchable_select_options(
    name: :purview_circles,
    scope: -> {
      Circle.where(active: true, level: [:village, :mandal, :district, :state,
                                         :municipality, :corporation, :mp_constituency, :mla_constituency])
            .or(Circle.where(id: 0))
    },
    display_text: ->(record) do
      "#{record.id}, #{record.name}, #{record.level}"
    end,
    text_attribute: :name_en,
    filter: lambda do |term, scope|
      scope.ransack(m: 'or', id_start: term, name_en_cont: term, short_name_cont: term).result
    end
  )

  searchable_select_options(
    name: :garuda_location_circles,
    scope: -> {
      Circle.where(active: true, level: [:state, :district])
            .or(Circle.where(id: 0))
    },
    display_text: ->(record) do
      "#{record.id}, #{record.name}, #{record.level}"
    end,
    text_attribute: :name_en,
    filter: lambda do |term, scope|
      scope.ransack(m: 'or', id_start: term, name_en_cont: term, short_name_cont: term).result
    end
  )

  searchable_select_options(
    name: :garuda_party_circles,
    scope: -> {
      Circle.where(active: true, level: [:political_party])
    },
    display_text: ->(record) do
      "#{record.id}, #{record.name}, #{record.level}"
    end,
    text_attribute: :name_en,
    filter: lambda do |term, scope|
      scope.ransack(m: 'or', id_start: term, name_en_cont: term, short_name_cont: term).result
    end
  )

  # get all circles for searchable select
  searchable_select_options(name: :all_circles,
                            scope: -> { Circle.where(active: true).order(id: :asc) },
                            text_attribute: :name_en,
                            display_text: ->(record) {
                              "#{record.name_en}, #{record.circle_type}, #{record.level}"
                            })

  # See permitted parameters documentation:
  # https://github.com/activeadmin/activeadmin/blob/master/docs/2-resource-customization.md#setting-up-strong-parameters
  #
  # Uncomment all parameters which should be permitted for assignment
  #
  # or
  #
  # permit_params do
  #   permitted = [:name, :name_en, :dynamic_link, :level, :parent_circle_id, :active, :photo_id, :description, :head_user_id, :circle_type, :hashid, :photo, :feed_type, :intro, :is_user_joined, :members_count, :new_posts_count, :last_posted_at, :level_verbose, :type_verbose]
  #   permitted << :other if params[:action] == 'create' && current_user.admin?
  #   permitted
  # end

  collection_action :get_level_options, method: :get do
    circle_type = params[:circle_type]
    levels = circle_type.present? ? Circle.level_options_for(circle_type) : Circle.levels.keys - ['sub']
    render json: { levels: levels }
  end

  action_item :create_sub_circle, only: :show do
    link_to 'Create Sub Circle', new_admin_circle_sub_circle_path(circle) if circle.eligibile_for_sub_circle_flow?
  end

  action_item :sub_circles, only: :show do
    link_to 'View Sub Circles', admin_circle_sub_circles_path(circle) if circle.eligibile_for_sub_circle_flow?
  end

  action_item :circle_package, only: :show do
    # Give add package option only if no active package (current or future) mappings are present
    if resource.circle_package_mappings.where(active: true).where('start_date >= :today or start_date <= :today and :today <= end_date', today: Time.zone.today).empty?
      link_to 'Add Circle Package', add_circle_package_admin_circle_path(circle)
    end
  end

  member_action :add_circle_package, method: :get do
    @circle = resource
    render 'add_circle_package'
  end

  member_action :update_circle_package, method: :get do
    @circle = resource
    @circle_package_mapping = CirclePackageMapping.find_by(id: params[:circle_package_mapping_id])
    render 'update_circle_package'
  end

  member_action :end_circle_package, method: :get do
    @circle = resource
    @circle_package_mapping = CirclePackageMapping.find_by(id: params[:circle_package_mapping_id])
    render 'end_circle_package'
  end

  member_action :add_circle_package_on_circle, method: :post do
    circle = resource
    attrs = params[:circle]

    circle_package_id = attrs[:circle_package_id].to_i if attrs[:circle_package_id].present?
    start_date = attrs[:start_date]
    end_date = attrs[:end_date]
    relationship_manager_id = attrs[:relationship_manager_id].to_i if attrs[:relationship_manager_id].present?

    if relationship_manager_id.blank?
      flash[:error] = 'Relationship manager is required'
      render 'add_circle_package'
      return
    end

    circle.relationship_manager_id = relationship_manager_id if relationship_manager_id.present?
    circle.circle_package_mappings.build(circle_package_id:, start_date:, end_date:)

    if circle.save
      redirect_to admin_circle_path(circle), notice: 'Circle package mapping created successfully'
    else
      flash[:error] = circle.errors.full_messages.to_sentence
      render 'add_circle_package'
    end
  end

  member_action :end_circle_package_on_circle, method: :post do
    circle = resource
    circle_attrs = params[:circle]
    circle_package_mapping = circle.circle_package_mappings.find_by(id: circle_attrs[:circle_package_mapping_id])

    package_attrs = {
      end_date: circle_attrs[:end_date]
    }.compact

    ActiveRecord::Base.transaction do
      circle_package_mapping.assign_attributes(package_attrs)
      circle_package_mapping.save!
      if circle_attrs[:relationship_manager_id].present?
        circle.relationship_manager_id = circle_attrs[:relationship_manager_id].to_i
      end
      circle.save! if circle_package_mapping.persisted?

      redirect_to admin_circle_path(circle), notice: 'Package end date updated successfully'
    end
  rescue => e
    errors = (circle_package_mapping.errors.full_messages + circle.errors.full_messages).to_sentence
    errors = e if errors.blank?
    flash[:error] = errors
    @circle = circle
    @circle_package_mapping = circle_package_mapping
    render 'end_circle_package'
  end

  member_action :update_circle_package_on_circle, method: :post do
    circle = resource
    circle_attrs = params[:circle]
    circle_package_mapping = circle.circle_package_mappings.find_by(id: circle_attrs[:circle_package_mapping_id])

    package_attrs = {
      circle_package_id: circle_attrs[:circle_package_id].present? ? circle_attrs[:circle_package_id].to_i : nil,
      start_date: circle_attrs[:start_date],
      end_date: circle_attrs[:end_date]
    }.compact

    ActiveRecord::Base.transaction do
      circle_package_mapping.assign_attributes(package_attrs)
      circle_package_mapping.save!
      if circle_attrs[:relationship_manager_id].present?
        circle.relationship_manager_id = circle_attrs[:relationship_manager_id].to_i
      end
      circle.save! if circle_package_mapping.persisted?

      redirect_to admin_circle_path(circle), notice: 'Package updated successfully'
    end
  rescue => e
    errors = (circle_package_mapping.errors.full_messages + circle.errors.full_messages).to_sentence
    flash[:error] = errors
    @circle = circle
    @circle_package_mapping = circle_package_mapping
    render 'update_circle_package'
  end

  action_item :send_fan_poster_to_channel, only: :show do
    if current_admin_user.role.to_sym.in?([:admin, :sales_am, :op_executive]) #|| circle.relationship_manager_id == current_admin_user.id
      link_to 'Send Fan Poster To Channel', send_fan_poster_to_channel_admin_circle_path(circle)
    end
  end

  member_action :send_fan_poster_to_channel, method: :get do
    @circle = resource
    render 'send_fan_poster_to_channel'
  end

  member_action :create_fan_poster, method: :post do
    attrs = params[:fan_poster]
    creative_kind = attrs[:creative_kind]
    photo_v3 = attrs[:photo_v3]
    send_dm_message_notification = attrs[:send_dm_message_notification]
    dm_text_message = attrs[:dm_text_message]
    start_date = attrs[:start_time]
    end_date = attrs[:end_time]
    poster_creative = PosterCreative.new(creative_kind: creative_kind,
                                         photo_v3: AdminMedium.new(blob_data: photo_v3, admin_user_id: current_admin_user.id),
                                         paid: false, primary: false,
                                         h1_leader_photo_ring_type: :light, h2_leader_photo_ring_type: :sticker,
                                         active: true, creator: current_admin_user, send_dm_message: true,
                                         start_time: start_date, end_time: end_date,
                                         send_dm_message_notification: send_dm_message_notification.to_i == 1,
                                         dm_text_message: dm_text_message)

    poster_creative.poster_creative_circles.build(circle_id: resource.id)

    if poster_creative.save
      flash[:notice] = 'Fan poster created sent to channel successfully'
      redirect_to admin_poster_creative_path(poster_creative)
    else
      flash[:error] = poster_creative.errors.full_messages.join(', ')
      redirect_to send_fan_poster_to_channel_admin_circle_path(resource)
    end
  end

  config.sort_order = 'id_asc'

  actions :all, except: [:destroy]

  index do
    selectable_column

    column :id do |circle|
      if circle.sub_level?
        link_to(circle.id, admin_circle_sub_circle_path(circle.parent_circle, circle))
      else
        link_to(circle.id, admin_circle_path(circle))
      end
    end
    column :name
    column :name_en
    column :short_name
    column :parent_circle_id do |circle|
      (!circle.parent_circle_id.nil? && circle.parent_circle_id > 0) ? link_to(circle.parent_circle.name, admin_circle_path(circle.parent_circle)) : ''
    end
    column :circle_type
    column :level
    column :active
    column :short_info
    column :actions, defaults: false do |circle|
      links = []
      links << link_to('Edit', edit_admin_circle_path(circle))
      safe_join(links, " ")
    end

    # column :created_at
    # column :updated_at
  end

  # action_item :only => :index do
  #   link_to 'Import/update villages', :action => 'upload_csv'
  # end

  collection_action :upload_csv do
    render 'upload_file/upload_csv_file'
  end

  collection_action :import_csv, method: :post do
    add_or_update_villages(params[:file])
    redirect_to action: :index, notice: 'CSV imported successfully!'
  end

  show do |circle|
    attributes_table do
      row :id
      row :name
      row :name_en
      row :short_name
      row :parent_circle
      row :singular_long_link do |c|
        link = "https://prajaapp.sng.link/A3x5b/8mt6?_dl=praja%3A%2F%2Fcircles/#{c.id}&_ddl=praja%3A%2F%2Fcircles/#{c.id}"
        link_to(link, link)
      end
      row :singular_long_link_members_tab do |c|
        link = "https://prajaapp.sng.link/A3x5b/8mt6?_dl=praja%3A%2F%2Fcircles/#{c.id}/members&_ddl=praja%3A%2F%2Fcircles/#{c.id}/members"
        link_to(link, link)
      end
      row :notification_deep_link do |c|
        "praja-app://buzz.praja.app/circles/#{c.id}"
      end
      row :notification_deep_link_members_tab do |c|
        "praja-app://buzz.praja.app/circles/#{c.id}/members"
      end
      row :posters_web_tool_link do |c|
        if (c.political_party_level? || c.political_leader_level?) && Circle.has_active_layout?(c.id)
          link = "https://praja.app/circles/#{c.hashid}/posters"
          link_to(link, link)
        end
      end
      row :photo do |c|
        image_tag c.photo.url, class: 'thumb_size' if c.photo.present?
      end
      row :banner do |c|
        image_tag c.banner.url, class: 'thumb_size' if c.banner.present?
      end
      row :slogan_icon do |c|
        image_tag c.slogan_icon.url, class: 'thumb_size' if c.slogan_icon.present?
      end
      row :circle_type
      row :level
      row :conversation_type
      row :active
      if circle.political_party_level?
        row :leader_photos do |c|
          c.circle_photos.where(photo_type: :circle).map(&:photo).map do |cp|
            image_tag cp.url, class: 'thumb_size', style: 'margin-right: 5px;' if cp.present?
          end
        end
      end
      row :poster_photos do |c|
        c.get_poster_photos.map(&:photo).map do |cp|
          image_tag cp.url, class: 'thumb_size', style: "margin-right: 5px;" if cp.present?
        end
      end
      row :short_info
      owner = circle.get_owner
      if owner.present?
        row :circle_owner do
          link_to(owner.name, admin_user_path(owner))
        end
      end
      row :created_at
      row :updated_at
      if circle.circle_package_mappings.where(active: true).where('start_date >= :today or (start_date <= :today and :today <= end_date)', today: Time.zone.today).exists?
        panel 'Package Info' do
          cps = circle.circle_package_mappings.where(active: true).where('start_date >= :today or (start_date <= :today and :today <= end_date)', today: Time.zone.today)
          table_for cps do
            column 'Package' do |cpm|
              link_to cpm.circle_package.name, admin_circle_package_path(cpm.circle_package)
            end
            column :usage do |cpm|
              if cpm.active? && cpm.start_date <= Time.zone.today && Time.zone.today <= cpm.end_date
                status_tag 'IN USE', class: 'in_use'
              else
                status_tag 'NOT IN USE', class: 'not_in_use'
              end
            end
            column :start_date
            column :end_date
            column :active
            column :relationship_manager do
              circle.relationship_manager
            end
            column :updated_at
            column :actions do |cpm|
              links = []
              if cpm.active?
                # If active only the circle mapping can be editable
                # If active then can update just the start date
                if cpm.start_date <= Time.zone.today && cpm.end_date >= Time.zone.today
                  links << link_to('Update End date', end_circle_package_admin_circle_path(circle, circle_package_mapping_id: cpm.id))
                  # If the mapping is the active mapping then it can be upgraded also
                  # either new mapping can be created or existing other active mapping can be updated
                  upgradable_id = cpm.get_upgradable_mapping_id
                  if upgradable_id.present?
                    links << link_to('Upgrade Package', update_circle_package_admin_circle_path(circle, circle_package_mapping_id: upgradable_id))
                  else
                    links << link_to('Upgrade Package', add_circle_package_admin_circle_path(circle))
                  end
                elsif cpm.start_date > Time.zone.today && cpm.end_date > Time.zone.today
                  links << link_to('Update Package', update_circle_package_admin_circle_path(circle, circle_package_mapping_id: cpm.id))
                end
              end
              safe_join(links, tag(:br))
            end
          end
        end
      end
    end

    panel 'Circle Relations' do
      if circle.first_circle_relations.present?
        table_for circle.first_circle_relations do
          column :second_circle
          column :relation
          column :created_at
          column :updated_at
          column 'Actions' do |relation|
            links = ''.html_safe
            links += link_to 'View', admin_circles_relation_path(relation)
            links += ' '
            links += link_to 'Edit', edit_admin_circles_relation_path(relation)
            links += ' '
            links += link_to 'Delete', admin_circles_relation_path(relation), method: :delete, data: { confirm: 'Are you sure?' }
            links
          end
        end
      end

      if circle.second_circle_relations.present?
        table_for circle.second_circle_relations do
          column :first_circle
          column :relation
          column :created_at
          column :updated_at
          column 'Actions' do |relation|
            links = ''.html_safe
            links += link_to 'View', admin_circles_relation_path(relation)
            links += ' '
            links += link_to 'Edit', edit_admin_circles_relation_path(relation)
            links += ' '
            links += link_to 'Delete', admin_circles_relation_path(relation), method: :delete, data: { confirm: 'Are you sure?' }
            links
          end
        end
      end
    end

    panel "Edit History" do
      table_for PaperTrail::Version
                  .from('versions FORCE INDEX (index_versions_on_item_type_and_item_id_and_id)')
                  .where(item_type: "Circle", item_id: circle.id)
                  .order(id: :desc)
                  .limit(10) do
        column("Item") { |v| v.item }
        column('Changes') do |v|
          if v.object_changes
            changes = YAML.safe_load(v.object_changes, permitted_classes: [Date, Time], aliases: true)
            filtered_changes = changes.reject { |field, _| field == "created_at" || field == "updated_at" }
            filtered_changes.map do |field, values|
              old_value = values[0].nil? ? 'nil' : values[0].to_s
              new_value = values[1].nil? ? 'nil' : values[1].to_s
              "#{field}: #{old_value} -> #{new_value}"
            end.join(', ')
          else
            'No changes recorded'
          end
        end
        column("Modified at") { |v| v.created_at }
        column("Admin") do |v|
          if v.whodunnit.nil?
            ''
          elsif v.whodunnit == 'unknown'
            'Unknown'
          else
            link_to AdminUser.find(v.whodunnit).email, [:admin, AdminUser.find(v.whodunnit)]
          end
        end
      end
    end

    panel "Circle Photos" do
      circle_photos = CirclePhoto.where(circle_id: circle.id)
      if circle_photos.present?
        photo_ids = circle_photos.pluck(:id)
        versions = PaperTrail::Version.where(item_type: 'CirclePhoto', item_id: photo_ids, event: 'create').order(id: :desc)
        versions_map = versions.each_with_object({}) do |version, map|
          map[version.item_id] ||= version
        end
        table_for circle_photos do |photo|
          column("Photo Id") { |photo| photo.photo_id }
          column("Photo Type") { |photo| photo.photo_type }
          column('Created At') { |photo| photo.created_at }
          column('Admin') do |photo|
            version = versions_map[photo.id]
            if version && version.whodunnit.present? && version.whodunnit != 'unknown'
              admin_user = AdminUser.find_by(id: version.whodunnit)
              admin_user ? link_to(admin_user.name, [:admin, admin_user]) : "Admin not found"
            else
              "Unknown"
            end
          end
        end
      end
    end
    active_admin_comments
  end

  form multipart: true do |f|
    f.semantic_errors
    f.inputs 'Circle Details' do
      f.input :name, required: true, input_html: { value: params[:name] || f.object&.name }
      f.input :name_en, required: true, input_html: { value: params[:name_en] || f.object&.name_en }
      f.input :short_name
      f.input :parent_circle_id
      f.input :photo, as: :file, hint: (image_tag(f.object.photo.url, class: 'thumb_size') if f.object.photo.present?)
      f.input :banner, as: :file, hint: (
        image_tag(f.object.banner.url, class: 'thumb_size') if f.object.banner.present?)
      f.input :slogan_icon, as: :file, hint: (image_tag(f.object.slogan_icon.url, class: 'thumb_size') if f.object.slogan_icon.present?)
      f.input :circle_type, required: true,
              as: :select,
              collection: Circle.circle_types.keys - ['sub'],
              include_blank: false,
              selected: params[:circle_type] || f.object&.circle_type || Circle.circle_types.keys.first
      f.input :level, required: true,
              as: :select,
              collection: Circle.levels.keys - ['sub'],
              include_blank: false,
              selected: params[:level] || f.object&.level || Circle.levels.keys.first,
              input_html: { data: { toggle: 'dynamic_visibility' } }
      # Show the :active field only on create
      if f.object.new_record? || current_admin_user.admin_role?
        f.input :active, wrapper_html: { class: 'active_input' }
      end
      f.input :conversation_type,
              as: :select,
              collection: Circle.conversation_types.keys - ['private_group'],
              include_blank: false,
              default: Circle.conversation_types[:none],
              input_html: { disabled: f.object.new_record? }
      f.input :short_info
      f.input :website_url
      f.input :leader_photo_1,
              as: :file,
              label: 'Leader Photo 1 (must be square)',
              wrapper_html: { class: 'leader_photos' },
              hint: (
                if f.object.circle_photos.where(photo_order: 1, photo_type: :circle).present?
                  image_tag(f.object.circle_photos.where(photo_order: 1, photo_type: :circle).first.photo.url, class: 'thumb_size')
                end
              )
      f.input :leader_photo_2,
              as: :file,
              label: 'Leader Photo 2 (must be square)',
              wrapper_html: { class: 'leader_photos' },
              hint: (
                if f.object.circle_photos.where(photo_order: 2, photo_type: :circle).present?
                  image_tag(f.object.circle_photos.where(photo_order: 2, photo_type: :circle).first.photo.url, class: 'thumb_size')
                end
              )
      f.has_many :poster_photos, allow_destroy: true, heading: 'Poster Photos' do |photo_form|
        photo_form.input :photo, as: :file, label: 'Poster Photo',
                         hint: (image_tag(photo_form.object.photo.url, class: 'thumb_size') if photo_form.object.photo.present?)
      end
      f.input :entity_type, as: :hidden, input_html: { value: f.object.entity_type.presence || params[:entity_type] }
      f.input :entity_id, as: :hidden, input_html: { value: f.object.entity_id.presence || params[:entity_id] }
    end
    f.actions
  end

  controller do

    before_action :check_admin_user, only: [:new, :edit, :destroy]

    def current_user
      current_admin_user
    end

    def check_admin_user
      begin
        authorize current_admin_user, :can_manage_circles?
      rescue Pundit::NotAuthorizedError
        redirect_to admin_circles_path, alert: 'You are not authorized to perform this action.'
      end
    end

    def update
      attrs = permitted_params[:circle]
      @circle = Circle.find_by_hashid!(params[:id])

      @circle.name = attrs[:name]
      @circle.name_en = attrs[:name_en]
      @circle.level = attrs[:level]
      @circle.circle_type = attrs[:circle_type]
      @circle.active = attrs[:active] if attrs[:active].present?
      @circle.conversation_type = attrs[:conversation_type]
      @circle.website_url = attrs[:website_url] unless attrs[:website_url].empty?
      @circle.short_name = attrs[:short_name] unless attrs[:short_name].nil?
      @circle.short_info = attrs[:short_info] unless attrs[:short_info].nil?

      @circle.photo = Photo.upload(attrs[:photo], Constants.praja_account_user_id) unless attrs[:photo].nil?
      @circle.banner = Photo.upload(attrs[:banner], Constants.praja_account_user_id) unless attrs[:banner].nil?
      @circle.slogan_icon = Photo.upload(attrs[:slogan_icon], Constants.praja_account_user_id) unless attrs[:slogan_icon].nil?

      @circle.parent_circle_id = attrs[:parent_circle_id] unless attrs[:parent_circle_id].nil?

      if attrs[:level].to_sym == :political_leader
        if @circle.get_poster_photos.blank? && attrs[:poster_photos_attributes].blank?
          error = 'Poster photo is required for political leader circle'
        end
      end

      unless attrs[:leader_photo_1].blank?
        valid = check_aspect_ratio(attrs[:leader_photo_1].path)
        error = 'Leader photo 1 uploaded is not a valid aspect ratio' unless valid
        photo = Photo.upload(attrs[:leader_photo_1], Constants.praja_account_user_id) if valid
        if valid && photo.present?
          @circle.circle_photos.where(photo_order: 1, photo_type: :circle).first_or_create.update(photo:)
        end
      end

      unless error.present? || attrs[:leader_photo_2].blank?
        valid = check_aspect_ratio(attrs[:leader_photo_2].path)
        error = 'Leader photo 2 uploaded is not a valid aspect ratio' unless valid
        photo = Photo.upload(attrs[:leader_photo_2], Constants.praja_account_user_id) if valid
        if valid && photo.present?
          @circle.circle_photos.where(photo_order: 2, photo_type: :circle).first_or_create.update(photo:)
        end
      end

      if (poster_photos_attrs = attrs[:poster_photos_attributes]&.to_unsafe_h).present?
        poster_photos_attrs.each do |index, value|
          if value[:_destroy] == "1" && value[:id].present?
            @circle.poster_photos.find(value[:id]).destroy
          elsif value[:photo].present?
            unless check_aspect_ratio(value[:photo].path)
              flash[:error] = "Poster photo at index order #{index} has an invalid aspect ratio."
              next
            end
            photo = Photo.upload(value[:photo], Constants.praja_account_user_id)
            next unless photo.present?
            existing_photo = @circle.poster_photos.find_or_initialize_by(id: value[:id], photo_type: :poster)
            if existing_photo.new_record?
              max_photo_order = @circle.poster_photos.where(photo_type: :poster).maximum(:photo_order) || 0
              existing_photo.photo_order = max_photo_order + 1
            end
            existing_photo.update(photo: photo) if photo.present?
          end
        end
      end

      if error.blank? && @circle.save
        redirect_to admin_circle_path(@circle)
      else
        if @circle.errors[:conversation_type].any?
          flash[:alert] = 'Please contact tech team'
        else
          flash[:error] = error
        end
        render :edit
      end
    end

    def create
      attrs = permitted_params[:circle]

      @circle = Circle.new
      @circle.name = attrs[:name]
      @circle.name_en = attrs[:name_en]
      @circle.short_name = attrs[:short_name]
      @circle.level = attrs[:level]
      @circle.circle_type = attrs[:circle_type]
      @circle.active = attrs[:active] if attrs[:active].present?
      @circle.website_url = attrs[:website_url] unless attrs[:website_url].empty?

      @circle.short_info = attrs[:short_info] unless attrs[:short_info].nil?

      @circle.photo = Photo.upload(attrs[:photo], Constants.praja_account_user_id) unless attrs[:photo].nil?

      @circle.banner = Photo.upload(attrs[:banner], Constants.praja_account_user_id) unless attrs[:banner].nil?

      @circle.slogan_icon = Photo.upload(attrs[:slogan_icon], Constants.praja_account_user_id) unless attrs[:slogan_icon].nil?

      @circle.parent_circle_id = attrs[:parent_circle_id] unless attrs[:parent_circle_id].nil?
      @circle.entity_type = attrs[:entity_type] if attrs[:entity_type].present?
      @circle.entity_id = attrs[:entity_id] if attrs[:entity_id].present?

      error = ''

      if attrs[:level].to_sym == :political_leader
        if attrs[:poster_photos_attributes].blank?
          error = 'Poster photo is required for political leader circle'
        end
      end

      unless error.present? || attrs[:leader_photo_1].blank?
        valid = check_aspect_ratio(attrs[:leader_photo_1].path)
        error = 'Leader photo 1 uploaded is not a valid aspect ratio' unless valid
        photo = Photo.upload(attrs[:leader_photo_1], Constants.praja_account_user_id) if valid
        @circle.circle_photos.build(photo: photo, photo_order: 1, photo_type: :circle) if valid && photo.present?
      end

      unless error.present? || attrs[:leader_photo_2].blank?
        valid = check_aspect_ratio(attrs[:poster_photo_2].path)
        error = 'Leader photo 2 uploaded is not a valid aspect ratio' unless valid
        photo = Photo.upload(attrs[:leader_photo_2], Constants.praja_account_user_id) if valid
        @circle.circle_photos.build(photo: photo, photo_order: 2, photo_type: :circle) if valid && photo.present?
      end

      if (poster_photos_attrs = attrs[:poster_photos_attributes]&.to_unsafe_h).present?
        poster_photos_attrs.each_with_index do |(_, value), i|
          next unless value[:photo].present?
          unless check_aspect_ratio(value[:photo].path)
            flash[:error] = "Poster photo at index order #{i} has an invalid aspect ratio."
            next
          end
          photo = Photo.upload(value[:photo], Constants.praja_account_user_id)
          next unless photo.present?
          @circle.poster_photos.build(photo: photo, photo_order: i, photo_type: :poster)
        end
      end

      if !error.present? && @circle.save
        entity_type = attrs[:entity_type]
        entity_id = attrs[:entity_id]
        check_relation = false

        if entity_type.present? && entity_type == 'User' && entity_id.present? && attrs[:level].to_sym == :political_leader
          user = User.find_by_id(entity_id)
          if user.present?
            check_relation = true
            relation = @circle.first_circle_relations.create(second_circle_id: user.mandal_id, relation: :Leader2Location)
          end
        end

        if check_relation
          notice = 'Circle relation is failed to create, save it manually' unless relation&.persisted?
        end
        redirect_to admin_circle_path(@circle), notice: notice
      else
        flash[:error] = error if error.present?
        flash[:error] = @circle.errors.full_messages.join(', ') if @circle.errors.any?
        render :new
      end
    end

    def check_aspect_ratio(file_path)
      w, h = FastImage.size(file_path)
      w.to_f / h.to_f == 1
    end

    def add_or_update_villages(csv_data)
      csv_file = csv_data.read
      CSV::Converters[:blank_to_nil] = lambda do |field|
        field && field.empty? ? nil : field
      end
      csv = CSV.new(csv_file, headers: true, header_converters: :symbol, converters: [:all, :blank_to_nil])
      # converting to an array of hashes
      csv_hash = csv.to_a.map { |row| row.to_hash }
      csv_hash.each do |row|
        village_id = row[:village_id]
        record = Circle.find_by_id(village_id) unless village_id.nil?
        if record.nil?
          Circle.create!(name_en: row[:village_name], name: row[:village_telugu_name], parent_circle_id: row[:mandal_id_parent_circle_id], level: :village, circle_type: :location)
        else
          record.update(
            name_en: row[:village_name],
            name: row[:village_telugu_name],
            parent_circle_id: row[:mandal_id_parent_circle_id]
          )
          record.save
        end
      end
    end
  end

  filter :id
  filter :name
  filter :name_en
  filter :short_name
  filter :parent_circle_id, label: 'Parent Circle ID'
  filter :circle_type, as: :select, collection: Circle.circle_types
  filter :level, as: :select, collection: Circle.levels
  filter :conversation_type, as: :select, collection: Circle.conversation_types
  filter :short_info
  filter :active
  filter :created_at
  filter :updated_at
end
