ActiveAdmin.register PosterCreative do
  menu priority: 3, :parent => 'Posters V2'
  permit_params :has_event, :creative_kind, :photo_v3, :photo_v2, :h1_leader_photo_ring_type, :h2_leader_photo_ring_type, :primary,
                :paid, :start_time, :end_time, :active, :designer_id, :scheduled_notification_job, circle_ids: []
  scope :all, default: true

  scope :scheduled do |pc|
    pc.where('active = ? AND start_time > ? AND end_time > ?', true, Time.zone.now, Time.zone.now)
  end

  scope :live do |pc|
    pc.where('active = ? AND start_time <= ? AND end_time > ?', true, Time.zone.now, Time.zone.now)
  end

  scope :expired do |pc|
    pc.where('active = ? AND end_time < ?', true, Time.zone.now)
  end

  scope :closed do |pc|
    pc.where(active: false)
  end

  actions :all, except: [:destroy]

  index do
    selectable_column
    id_column
    column :event do |pc|
      link_to(pc.event.name, admin_event_path(pc.event)) if pc.event.present?
    end
    column :paid
    column :circles do |pc|
      pc.poster_creative_circles.map do |poster_creative_circle|
        link_to(poster_creative_circle.circle.name, admin_circle_path(poster_creative_circle.circle)) if poster_creative_circle.present?
      end
    end
    column :start_time
    column :end_time
    column :status do |pcs|
      status = ''
      if pcs.active == false
        status = "<span style='color: #FF0000; font-weight: bold;'>Closed</span>"
      elsif pcs.active == true && pcs.start_time.present? && pcs.end_time.present?
        if pcs.start_time > Time.zone.now && pcs.end_time > Time.zone.now
          status = "<span style='color: #8B008B; font-weight: bold;'>Scheduled</span>"
        elsif pcs.start_time < Time.zone.now && pcs.end_time > Time.zone.now
          status = "<span style='color: #008000; font-weight: bold;'>Live</span>"
        elsif pcs.end_time < Time.zone.now
          status = "<span style='color: #FFA500; font-weight: bold;'>Expired</span>"
        else
          status = 'Unknown'
        end
      end
      status.html_safe
    end
    # column :active
    column :creator do |pc|
      if pc.creator_type == 'AdminUser' && pc.creator.present?
        link_to pc.creator.email, admin_admin_user_path(pc.creator.id)
      end
    end
    # column :created_at
    # column :updated_at
    actions name: "Actions"
  end

  show do
    attributes_table do
      row :id
      row :event do |pc|
        pc.event.present? ? link_to(pc.event.name, admin_event_path(pc.event)) : '-'
      end
      row :creative_kind
      row :photo_v2 do |pc|
        if pc.photo_v2.present?
          image_tag pc.photo_v2.url, class: 'thumb_size', style: 'margin-right: 5px;'
        end
      end
      row :photo_v3 do |pc|
        if pc.photo_v3.present?
          image_tag pc.photo_v3.url, class: 'thumb_size', style: 'margin-right: 5px;'
        end
      end
      row :paid
      row :circles do |pc|
        pc.poster_creative_circles.map do |poster_creative_circle|
          link_to(poster_creative_circle.circle.name, admin_circle_path(poster_creative_circle.circle)) if poster_creative_circle.present?
        end
      end
      row :start_time
      row :end_time
      row :h1_leader_photo_ring_type
      row :h2_leader_photo_ring_type
      row :primary
      row :active
      row :designer
      row :creator do |pc|
        link_to(pc.creator.email, admin_admin_user_path(pc.creator)) if pc.creator.present?
      end
      row :poster_to_dm_scheduled_jid do |pc|
        Metadatum.where(entity: pc, key: Constants.poster_creative_jid_key).first&.value
      end
      row :created_at
      row :updated_at

      # Show a separate table for whatsapp campaign links
      # show campaign links only for event not present creatives
      if resource.poster_creative_circles.present?
        panel 'Creative deep link for individual circle' do
          table_for resource.poster_creative_circles do
            column :circle do |pcc|
              link_to(pcc.circle.name, admin_circle_path(pcc.circle))
            end

            # If event is present, then the deep link need event_id because it is mandatory to find the event of that creative_id
            # If event is not present, then the deep link will not have event_id
            column :deep_link do |pcc|
              div do
                if resource.event_id.present?
                  link = "praja-app://buzz.praja.app/posters/layout?creative_id=#{resource.id}&circle_id=#{pcc.circle.id}&id=#{resource.event_id}"
                else
                  link = "praja-app://buzz.praja.app/posters/layout?creative_id=#{resource.id}&circle_id=#{pcc.circle.id}"
                end

                span link
                button class: 'copy-link-button', "data-link": link do
                  icon('fa-regular', 'copy') + " Copy"
                end
              end
            end
          end
        end
        # panel 'Selected Creative Poster Web Tool Links [This Selected Creative will come first]' do
        #   table_for resource.poster_creative_circles do
        #     column :circle do |pcc|
        #       link_to(pcc.circle.name, admin_circle_path(pcc.circle))
        #     end
        #
        #     column :poster_web_tool_link do |pcc|
        #       circle = pcc.circle
        #       is_political_or_leader = circle.political_leader_level? || circle.political_party_level?
        #       has_active_layout = Circle.has_active_layout?(circle.id)
        #
        #       div do
        #         if !is_political_or_leader
        #           span 'Not a Political party or Leader circle'
        #         elsif !has_active_layout
        #           span 'Circle has no active layout'
        #         else
        #           circle_hash_id = circle.hashid
        #           poster_creative_id = resource.id
        #           link = "https://praja.app/circles/#{circle_hash_id}/posters?creative_id=#{poster_creative_id}"
        #
        #           span link
        #           button class: 'copy-link-button', "data-link": link do
        #             icon('fa-regular', 'copy') + " Copy"
        #           end
        #         end
        #       end
        #     end
        #   end
        # end
      end
    end
  end

  form :html => { :multipart => true } do |f|
    f.semantic_errors
    f.inputs 'Poster Creative Details' do
      if f.object.new_record?
        f.input :has_event, as: :boolean, label: 'Has Event', input_html: { checked: true, id: 'has_event_checkbox', data: { if: 'changed', then: 'callback has_event_toggled' } }
        f.input :event_id, as: :searchable_select, ajax: { resource: Event, collection_name: :events }, wrapper_html: { class: 'has_event' }, label: 'Event ID', input_html: { "data-placeholder": "Search by ID or Name.." }
        f.input :creative_kind, as: :select, collection: PosterCreative.creative_kinds.keys
        f.input :paid, input_html: { id: 'has_paid_checkbox', data: { if: 'checked', then: 'hide', target: '.is_paid' } }
        f.input :photo_v2, as: :file, hint: 'aspect ratio - 630x940'
        f.input :photo_v3, as: :file, hint: 'aspect ratio - 800x1000'
        f.input :primary
        f.input :h1_leader_photo_ring_type, as: :select, collection: PosterCreative.h1_leader_photo_ring_types.keys
        f.input :h2_leader_photo_ring_type, as: :select, collection: PosterCreative.h2_leader_photo_ring_types.keys
        f.input :designer_id, as: :searchable_select, ajax: { resource: AdminUser, collection_name: :designers }, input_html: { "data-placeholder": "Search by ID or email.." }
        f.input :start_time, as: :datetime_picker, hint: "If blank & event is present, event's start time will be used"
        f.input :end_time, as: :datetime_picker, hint: "If blank & event is present, event's end time will be used"
        f.input :circle_ids,
                label: 'Circle(s)',
                as: :searchable_select,
                multiple: true, ajax: { resource: Circle, collection_name: :poster_circles },
                input_html: { "data-placeholder": 'Circle id or Name or Short name' },
                wrapper_html: { class: 'no_event' }
        f.input :active
      else
        if f.object.event_id.present?
          f.input :event, input_html: { disabled: true, value: f.object.event&.name }
        end
        f.input :creative_kind, as: :select, collection: PosterCreative.creative_kinds.keys
        f.input :h1_leader_photo_ring_type, as: :select, collection: PosterCreative.h1_leader_photo_ring_types.keys
        f.input :h2_leader_photo_ring_type, as: :select, collection: PosterCreative.h2_leader_photo_ring_types.keys
        f.input :designer_id, as: :searchable_select, ajax: { resource: AdminUser, collection_name: :designers }, input_html: { "data-placeholder": "Search by ID or email.." }
        f.input :start_time, as: :datetime_picker, input_html: { id: 'creative_start_time' }
        f.input :end_time, as: :datetime_picker
        f.input :primary
        f.input :active, input_html: { id: 'creative_active_checkbox' }
        f.input :paid if f.object.event_id.present?
        if f.object.event_id.blank?
          f.input :circle_ids,
                  label: 'Circle(s)',
                  as: :searchable_select,
                  multiple: true,
                  ajax: { resource: Circle, collection_name: :poster_circles },
                  input_html: { "data-placeholder": 'Circle id or Name or Short name' },
                  hint: "Existing circles #{f.object.poster_creative_circles.map { |pcc| pcc.circle.id }.join(', ')}"
        end

        if Metadatum.where(entity: f.object, key: Constants.poster_creative_jid_key).exists?
          f.input :scheduled_notification_job, as: :hidden, input_html: { id: 'scheduled_notification_job' }
        end
        # TODO: send message to dm has been ignored for edit action
      end
    end
    f.actions
  end

  controller do
    def update
      attrs = params[:poster_creative]

      @poster_creative = PosterCreative.find(params[:id])
      @poster_creative.active = attrs[:active]
      @poster_creative.primary = attrs[:primary]
      @poster_creative.paid = attrs[:paid] if attrs[:paid].present?
      @poster_creative.start_time = attrs[:start_time]
      @poster_creative.end_time = attrs[:end_time]
      @poster_creative.h1_leader_photo_ring_type = attrs[:h1_leader_photo_ring_type]
      @poster_creative.h2_leader_photo_ring_type = attrs[:h2_leader_photo_ring_type]
      @poster_creative.creative_kind = attrs[:creative_kind]
      @poster_creative.designer_id = attrs[:designer_id]
      

      if @poster_creative.event_id.blank?
        circle_ids = attrs[:circle_ids].compact_blank.map(&:to_i)

        deleting_circle_ids = []
        if circle_ids.present?
          existing_circle_ids = @poster_creative.poster_creative_circles.map(&:circle_id)

          new_circle_ids = circle_ids - existing_circle_ids
          deleting_circle_ids = existing_circle_ids - circle_ids

          if new_circle_ids.present?
            new_circle_ids.each do |new_circle_id|
              @poster_creative.poster_creative_circles.build(circle_id: new_circle_id)
            end
          end
          if new_circle_ids.present? || deleting_circle_ids.present?
            IndexCreativesForPostersFeed.perform_async("creative_#{params[:id]}")
          end
        end
      end

      if @poster_creative.save
        @poster_creative.poster_creative_circles.where(circle_id: deleting_circle_ids).destroy_all if @poster_creative.event_id.blank? && deleting_circle_ids.present?
        redirect_to admin_poster_creative_path(@poster_creative)
      else
        flash[:error] = @poster_creative.errors.full_messages.first
        render :edit
      end
    end

    def create
      attrs = params[:poster_creative]
      create_another = params[:create_another]

      circle_ids = []
      if attrs[:event_id].present?
        circle_ids = EventCircle.where(event_id: attrs[:event_id]).pluck(:circle_id)
      else
        # Ensure paid is false for non-event poster creatives
        attrs[:paid] = false
      end

      circle_ids = attrs[:circle_ids].compact_blank unless circle_ids.present?

      if attrs[:photo_v2].blank? && !([:manifesto, :prepoll].include?(attrs[:creative_kind].to_sym))
        flash[:error] = 'Please upload photo_v2'
        return redirect_to new_admin_poster_creative_path
      end

      if attrs[:photo_v2].present?
        photo_v2 = AdminMedium.new(blob_data: attrs[:photo_v2], admin_user_id: current_admin_user.id)
      end

      if circle_ids.present?
        @poster_creative = PosterCreative.new(creative_kind: attrs[:creative_kind],
                                              photo_v3: AdminMedium.new(blob_data: attrs[:photo_v3], admin_user_id: current_admin_user.id),
                                              photo_v2: photo_v2,
                                              paid: attrs[:paid],
                                              primary: attrs[:primary],
                                              h1_leader_photo_ring_type: attrs[:h1_leader_photo_ring_type],
                                              h2_leader_photo_ring_type: attrs[:h2_leader_photo_ring_type],
                                              designer_id: attrs[:designer_id],
                                              start_time: attrs[:start_time],
                                              end_time: attrs[:end_time],
                                              active: attrs[:active],
                                              creator: current_admin_user)
        @poster_creative.event_id = attrs[:event_id] if attrs[:event_id].present?

        circle_ids.each do |circle_id|
          @poster_creative.poster_creative_circles.build(poster_creative_id: @poster_creative.id, circle_id: circle_id)
        end

        if @poster_creative.save
          flash[:notice] = 'Poster Creative created successfully'
          if create_another == 'on'
            redirect_to new_admin_poster_creative_path
          else
            redirect_to admin_poster_creative_path(@poster_creative)
          end
        else
          flash[:error] = @poster_creative.errors.full_messages.first
          render :new
        end
      else
        message = 'Please select the event' if attrs[:has_event].to_i == 1 && attrs[:event_id].blank?
        message = 'Please select the circle' if attrs[:has_event].to_i == 0 && circle_ids.blank?
        redirect_to new_admin_poster_creative_path, alert: message
      end
    end

    def scoped_collection
      super.tap do |scope|
        if params[:status].present?
          case params[:status]
          when 'scheduled'
            scope.where('active = ? AND start_time > ? AND end_time > ?', true, Time.zone.now, Time.zone.now)
          when 'live'
            scope.where('active = ? AND start_time <= ? AND end_time > ?', true, Time.zone.now, Time.zone.now)
          when 'expired'
            scope.where('active = ? AND end_time < ?', true, Time.zone.now)
          when 'closed'
            scope.where(active: false)
          end
        end
      end
    end
  end

  filter :creative_kind, as: :select, collection: PosterCreative.creative_kinds
  filter :id, label: 'Poster Creative ID'
  filter :start_time
  filter :end_time
  filter :paid
  filter :poster_creative_circle, label: 'Circle ID (use \'00\' for filtering \'public\' circle)', as: :numeric, filters: [:eq]
  filter :live_poster_creative, as: :select, collection: [%w[Yes yes], %w[No no]], label: 'Live', filters: [:eq]
  filter :designer_id, as: :searchable_select, ajax: { resource: AdminUser, collection_name: :designers },
         input_html: { "data-placeholder": "Search by ID or email.." }
  filter :primary
  filter :active
  filter :h1_leader_photo_ring_type, as: :select, collection: PosterCreative.h1_leader_photo_ring_types
  filter :h2_leader_photo_ring_type, as: :select, collection: PosterCreative.h2_leader_photo_ring_types
  filter :created_at
  filter :updated_at

  sidebar :filter_status, only: :index do
    render partial: 'admin/poster_creatives/status_filter'
  end
end
