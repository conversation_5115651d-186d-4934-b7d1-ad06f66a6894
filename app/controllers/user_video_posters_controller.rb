class UserVideoPostersController < ApiController
    before_action :set_logged_in_user, except: [:video_poster_generation_failed, :video_poster_generation_completed]

    def show
      video_poster = UserVideoPoster.find_by(id: params[:id], user_id: @user.id)

      if video_poster.nil?
        render json: { message: I18n.t('errors.video_poster_not_found') }, status: :not_found
        return
      end

      render json: video_poster.get_json, status: :ok
    rescue StandardError => e
      Honeybadger.notify(e, context: { video_poster_id: params[:id], user_id: @user&.id })
      render json: { message: I18n.t('errors.internal_server_error') }, status: :internal_server_error
    end

    def create
      video_frame_id = params[:video_frame_id]
      video_creative_id = params[:video_creative_id]
      video_creative_gradient_circle_id = params[:gradient_circle_id]

      if video_frame_id.blank?
        render json: { message: I18n.t('errors.missing_user_video_frame_id') }, status: :bad_request
        return
      end

      if video_creative_id.blank?
        render json: { message: I18n.t('errors.missing_video_creative_id') }, status: :bad_request
        return
      end

      user_video_frame = UserVideoFrame.where(user_id: @user.id, video_frame_id: video_frame_id, active: true).last
      if user_video_frame.nil?
        render json: { message: I18n.t('errors.user_video_frame_not_found') }, status: :not_found
        return  
      end

      video_creative = VideoCreative.find_by(id: video_creative_id)
      if video_creative.nil?
        render json: { message: I18n.t('errors.video_creative_not_found') }, status: :not_found
        return
      end

      # Check if video poster already exists
      video_posters = UserVideoPoster.where( user_video_frame_id: user_video_frame.id, source_video_id: video_creative.video_id, active: true )

      video_poster = video_posters.find do |vp|
        vp.metadata&.dig('video_creative_gradient_circle_id').to_s == video_creative_gradient_circle_id.to_s
      end

      if video_poster.present?
          if video_poster.failed?
            # Retry logic for failed video posters - reset to pending and retrigger
            video_poster.mark_as_pending!
          elsif !video_poster.failed?
            # Return existing non-failed poster
            render json: video_poster.get_json, status: :ok
            return
          end
      else
        # Create new video poster
        begin
          video_poster = UserVideoPoster.create!(
            user_video_frame_id: user_video_frame.id,
            source_video_id: video_creative.video_id,
            metadata: { video_creative_gradient_circle_id: video_creative_gradient_circle_id }.compact,
            user_id: @user.id
          )
        rescue ActiveRecord::RecordNotUnique
          # If there's a race condition and another request created the same poster, find it
          video_poster = UserVideoPoster.find_by!(
            user_video_frame_id: user_video_frame.id,
            source_video_id: video_creative.video_id
          )
          # If the found poster is failed, retry it
          if video_poster.failed?
            video_poster.mark_as_pending!
          end
        end
      end

      # Trigger generation if poster can be processed
      if video_poster.may_process?
        VideoPostersGenerationTrigger.perform_async(video_poster.id)
      end

      render json: video_poster.get_json, status: :ok
    rescue StandardError => e
      Honeybadger.notify(e, context: {
        user_video_frame_id: user_video_frame.id,
        video_creative_id: video_creative_id,
        user_id: @user&.id
      })
      render json: { message: I18n.t('errors.internal_server_error') }, status: :internal_server_error
    end
  
    def video_poster_generation_failed
      job_id = params[:job_id]
      error_code = params[:error_code] || 'unknown'

      if job_id.blank?
        render json: { message: I18n.t('errors.job_id_missing') }, status: :bad_request
        return
      end

      video_poster = UserVideoPoster.find_by(job_id: job_id)
      if video_poster.nil?
        render json: { message: I18n.t('errors.video_poster_not_found') }, status: :not_found
        return
      end

      if video_poster.may_fail?
        video_poster.fail!(error_code)
        render json: { video_poster_id: video_poster.id, job_id: video_poster.job_id, status: video_poster.status }, status: :ok
      else
        render json: { message: I18n.t('errors.invalid_state_transition') }, status: :unprocessable_entity
      end
    rescue StandardError => e
      Honeybadger.notify(e, context: { job_id: job_id, error_code: error_code })
      render json: { message: I18n.t('errors.internal_server_error') }, status: :internal_server_error
    end

    def video_poster_generation_completed
      job_id = params[:job_id]
      video_url = params[:video_url]
      thumbnail_url = params[:thumbnail_url]
      width = params[:width]
      height = params[:height]
      duration = params[:duration]
      bitrate = params[:bitrate]

      if job_id.blank?
        render json: { message: I18n.t('errors.job_id_missing') }, status: :bad_request
        return
      end

      if video_url.blank? || thumbnail_url.blank?
        render json: { message: I18n.t('errors.missing_video_data') }, status: :bad_request
        return
      end

      video_poster = UserVideoPoster.find_by(job_id: job_id)
      if video_poster.nil?
        render json: { message: I18n.t('errors.video_poster_not_found') }, status: :not_found
        return
      end

      if video_poster.may_complete?
        # Pass callback data directly to the complete method
        callback_data = {
          video_url: video_url,
          thumbnail_url: thumbnail_url,
          width: width&.to_i,
          height: height&.to_i,
          duration: duration&.to_f,
          bitrate: bitrate&.to_f
        }
        video_poster.complete_with_callback_data!(callback_data)
        render json: { video_poster_id: video_poster.id, job_id: video_poster.job_id, status: video_poster.status }, status: :ok
      else
        render json: { message: I18n.t('errors.invalid_state_transition') }, status: :unprocessable_entity
      end
    rescue StandardError => e
      Honeybadger.notify(e, context: {
        job_id: job_id,
        video_url: video_url,
        thumbnail_url: thumbnail_url,
        width: width,
        height: height,
        duration: duration,
        bitrate: bitrate
      })
      render json: { message: I18n.t('errors.internal_server_error') }, status: :internal_server_error
    end

    def video_frames_mark_as_seen_bulk
      unless params[:video_frame_ids].present?
        render json: { success: false }, status: :bad_request
        return
      end
      logger.debug "marking as seen for user: #{@user.id} and video frames: #{params[:video_frame_ids]}"

      all_video_frames = params[:video_frame_ids].keys
      all_video_frames.each_slice(50) do |video_frame_ids|
        video_frame_with_timestamps = video_frame_ids.map { |video_frame_id| [video_frame_id, params[:video_frame_ids][video_frame_id]] }.to_h
        logger.debug "Marking as seen for user: #{@user.id} and video frames: #{video_frame_with_timestamps}"
        QueueVideoFrameViews.perform_async(@user.id, video_frame_with_timestamps)
      end
      render json: { success: true }, status: :ok
    end

    def video_creatives_mark_as_seen_bulk
      unless params[:video_creative_ids].present?
        render json: {success: false}, status: :bad_request
        return
      end
      logger.debug "Marking as seen for user: #{@user.id} and creatives: #{params[:video_creative_ids]}"

      all_video_poster_creatives = params[:video_creative_ids].keys

      all_video_poster_creatives.each_slice(50) do |video_creative_ids|
        video_creative_with_timestamps = video_creative_ids.map { |video_creative_id| [video_creative_id, params[:video_creative_ids][video_creative_id]] }.to_h
        logger.debug "Marking as seen for user: #{@user.id} and creatives: #{video_creative_with_timestamps}"
        QueueVideoCreativeViews.perform_async(@user.id, video_creative_with_timestamps)
      end
      render json: { success: true }, status: :ok
    end

    def share
      if params[:method].blank? || params[:video_creative_id].blank?
        render json: { message: "Missing required parameters" }, status: :bad_request
        return
      end

      unless VideoPosterShare.methods.values.include?(params[:method])
        render json: { message: "Invalid video poster share method" }, status: :bad_request
        return
      end

      video_frame_id = params[:video_frame_id].to_i
      video_creative_id = params[:video_creative_id].to_i
      user_video_poster_id = params[:video_poster_id].to_i

      VideoPosterShare.create!(
        user: @user,
        video_creative_id: video_creative_id,
        video_frame_id: video_frame_id,
        user_video_poster_id: user_video_poster_id,
        method: params[:method],
        actioned_at: Time.zone.now
      )

      # Note: For now the deep link is disabled for video posters
      # deeplink_url = @user.deeplink_url_in_poster_share(params[:method], creative_id: video_creative_id, format_type: "video")

      render json: { success: true }, status: :ok
    rescue StandardError => e
      render json: { success: false, message: "Failed to log video poster share", error: "#{e}" }, status: :internal_server_error
    end

end
