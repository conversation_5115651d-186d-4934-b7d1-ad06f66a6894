# frozen_string_literal: true

class UserVideoPoster < ApplicationRecord
  include AASM

  enum status: {
    pending: 'pending',
    processing: 'processing',
    completed: 'completed',
    failed: 'failed',
  }

  # Set default status
  attribute :status, default: 'pending'

  belongs_to :user_video_frame
  belongs_to :source_video, class_name: 'Video'
  belongs_to :user
  belongs_to :generated_video, class_name: 'Video', optional: true

  # Delegate video_frame access through user_video_frame
  delegate :video_frame, to: :user_video_frame

  validates :user_id, presence: true
  validates :source_video_id, presence: true
  validates :user_video_frame_id, presence: true

  before_create :add_job_id

  # Store error_code in metadata JSON field
  def error_code
    metadata&.dig('error_code')
  end

  def error_code=(value)
    self.metadata = (metadata || {}).merge('error_code' => value)
  end

  def add_job_id
    self.job_id = ULID.generate
  end

  # Instance variable to store callback data temporarily
  attr_accessor :callback_data_for_completion

  # Method to complete with callback data
  def complete_with_callback_data!(callback_data = {})
    self.callback_data_for_completion = callback_data
    complete!
  end

  aasm column: :status, enum: true do
    state :pending, initial: true
    state :processing
    state :completed
    state :failed
    event :process, after_commit: :call_back_to_dm do
      transitions from: :pending, to: :processing
    end

    event :complete, after_commit: :call_back_to_dm do
      before do
        save_generated_video_object(callback_data_for_completion || {})
      end
      transitions from: :processing, to: :completed
    end

    event :fail, after_commit: :call_back_to_dm do
      before do |error_code|
        generation_failed(error_code)
      end
      transitions from: :processing, to: :failed
    end

    event :mark_as_pending do
      transitions from: :failed, to: :pending
    end
  end

  def call_back_to_dm
    req_body = { userId: user_id.to_s, data: { user_id: user_id, video_poster: get_json } }
    req_url = "#{Constants.get_dm_url}/video-posters/status-updated"
    response = DmUtil.put_request_to_dm(req_url, req_body)
    if response.code.to_i != 200
      Honeybadger.notify("Failed to update video poster status in DM. Response code: #{response.code} for data #{req_body}")
    end
  end

  def after_completed
    call_back_to_dm
  end

  def after_failed
    call_back_to_dm
  end

  def generation_failed(error_code = 'unknown')
    self.error_code = error_code
  end

  def save_generated_video_object(callback_data = {})
    if generated_video_id.nil?
      # Use callback data if available, otherwise fall back to default URLs
      data = callback_data || {}

      video_attributes = {
        url: "https://ruv-cdn.thecircleapp.in/video-posters/#{job_id}-video.mp4",
        thumbnail_url: "https://a-cdn.thecircleapp.in/production/video-posters/#{job_id}-thumbnail.jpg",
        user_id: user_id,
        status: :processed,
        service: :aws,
      }

      # Add video metadata if available from callback
      video_attributes[:width] = data[:width] || data['width'] if (data[:width] || data['width']).present?
      video_attributes[:height] = data[:height] || data['height'] if (data[:height] || data['height']).present?
      video_attributes[:duration] = data[:duration] || data['duration'] if (data[:duration] || data['duration']).present?
      video_attributes[:bitrate] = data[:bitrate] || data['bitrate'] if (data[:bitrate] || data['bitrate']).present?

      video = Video.create!(video_attributes)
      self.generated_video = video
    end
  end

  def get_json
    video = generated_video if completed?
    {
      id: id,
      user_video_frame_id: user_video_frame_id,
      video_frame_id: user_video_frame&.video_frame_id,
      video: video,
      status: status,
      error_code: error_code,
    }
  end


  def lambda_payload
    mode = user_video_frame.get_video_mode
    frame_dimensions = user_video_frame.get_frame_dimensions(mode)
    gradient_circle_id = self.metadata&.dig('video_creative_gradient_circle_id')
    elements = user_video_frame.get_video_creative_elements(source_video: self.source_video, gradient_circle_id: gradient_circle_id)
    {
      "elements": elements,
      "frame_height": frame_dimensions[:height],
      "frame_width": frame_dimensions[:width],
      "callback_url": "#{Constants.get_api_host}/video-posters",
      "job_id": job_id
    }
  end
end
