# frozen_string_literal: true

# Shared concern for identity photo generation functionality
# Used by workers that need to generate identity photos for users
module IdentityPhotoGeneration
  extend ActiveSupport::Concern

  included do
    include Capture
  end

  private

  # Generates an identity image for a user and video frame
  # @param user [User] The user to generate the image for
  # @param video_frame [VideoFrame] The video frame configuration to use
  # @return [String] The CDN URL of the generated identity image
  def generate_identity_image(user, video_frame)
    # Get user data for identity image
    user_name = user.name
    badge_description = user.get_badge_role_including_unverified&.get_json&.dig('description')
    
    # Get font information from video frame
    font = video_frame.font
    name_font_family = font.name_font
    badge_font_family = font.badge_font
    
    # Generate HTML content
    html = generate_html(
      template_name: 'identity_photo_template',
      video_type: video_frame.video_type,
      user_name: user_name,
      badge_description: badge_description,
      name_font_family: name_font_family,
      badge_font_family: badge_font_family
    )
  
    # Capture HTML as image
    uploaded_image = capture_html_as_image(html, '#top-outer-container')
    raise "Did not receive url from captured html - #{uploaded_image}" unless uploaded_image['cdn_url'].present?

    uploaded_image['cdn_url']
  end

  # Generates HTML content for identity photo template
  # @param template_name [String] The name of the template to use
  # @param video_type [String] The video type (PORTRAIT, LANDSCAPE, SQUARE)
  # @param user_name [String] The user's name
  # @param badge_description [String] The user's badge description
  # @param name_font_family [String] The font family for the name
  # @param badge_font_family [String] The font family for the badge
  # @return [String] The rendered HTML content
  def generate_html(template_name:, video_type:, user_name:, badge_description:, name_font_family:, badge_font_family:)
    # Get dimensions and styling based on video type
    dimensions = UserVideoFrame::IDENTITY_IMAGE_DIMENSIONS[video_type]
    user_photo_dimensions = UserVideoFrame::USER_PHOTO_DIMENSIONS[video_type]

    # Calculate font sizes based on name length with improved logic
    name_font_size = calculate_name_font_size_with_video_type(user_name, video_type)
    # Determine user photo width based on video type (0px for portrait, actual width for landscape)
    user_photo_width = video_type.to_sym == :PORTRAIT ? 0 : user_photo_dimensions[:width] + 70

    locals = {
      container_width: dimensions[:width]*2,
      container_height: dimensions[:height]*2,
      name_font_size: name_font_size,
      badge_font_size: UserVideoFrame::IDENTITY_BADGE_FONT_SIZE,
      name_font_family: name_font_family,
      badge_font_family: badge_font_family,
      user_name: user_name,
      badge_description: badge_description,
      user_photo_width: user_photo_width,
    }

    ActionController::Base.render(
      inline: File.read(Rails.root.join('app', 'views', "#{template_name}.erb")),
      locals: locals
    )
  end

  # Calculates the appropriate font size based on the user's name length
  # @param user_name [String] The user's name
  # @return [Integer] The calculated font size
  def calculate_name_font_size_with_video_type(user_name, video_type)
    name_length = user_name.length

    # Font sizes calculated based on available text space:
    # Portrait: Full width (720px) available for text
    # Landscape/Square: Reduced width (534px) due to user photo, so smaller fonts needed

    if video_type.to_sym == :PORTRAIT
      case name_length
      when 0..38
        40
      when 38..45
        32
      else
        26
      end
    else
      # Landscape and Square modes have ~25% less horizontal space due to user photo
      # Font sizes reduced proportionally to ensure text fits properly
      case name_length
      when 0..30
        30
      when 30..40
        26
      when 40..50
        22
      when 50..60
        20
      else
        18
      end
    end
  end
end
