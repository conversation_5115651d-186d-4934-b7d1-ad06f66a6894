class UserVideoFrame < ApplicationRecord
  belongs_to :user
  belongs_to :video_frame

  validates :user_id, presence: true
  validates :video_frame_id, presence: true
  validates :identity_photo_url, presence: true

  has_many :user_video_posters, dependent: :destroy

  scope :active, -> { where(active: true) }

  has_paper_trail


  FRAME_DIMENSIONS = {
    'PORTRAIT' => { width: 360, height: 550 },
    'LANDSCAPE' => { width: 360, height: 358 },
    'SQUARE' => { width: 360, height: 500 }
  }.freeze

  VIDEO_DIMENSIONS = {
    'PORTRAIT' => { width: 360, height: 550 },
    'LANDSCAPE' => { width: 330, height: 188 },
    'SQUARE' => { width: 330, height: 330 }
  }.freeze

  USER_PHOTO_DIMENSIONS = {
    'PORTRAIT' => { width: 180, height: 202 },
    'LANDSCAPE' => { width: 116, height: 130 },
    'SQUARE' => { width: 116, height: 130 }
  }.freeze

  # Spacing constants from commented constants
  FRAME_PADDING = 16
  VIDEO_PADDING = 15
  IDENTITY_SPACING = 8
  PROTOCOL_IMAGE_WIDTH = 360  # protocolImageWidth from comments (missing but inferred)
  PROTOCOL_IMAGE_HEIGHT = 104  # protocolImageHeight from comments
  IDENTITY_PLATE_WIDTH = 360
  IDENTITY_PLATE_HEIGHT = 50

    # Identity image dimensions for different orientations
  IDENTITY_IMAGE_DIMENSIONS = {
    'PORTRAIT' => { width: IDENTITY_PLATE_WIDTH, height: IDENTITY_PLATE_HEIGHT },
    'LANDSCAPE' => { width: IDENTITY_PLATE_WIDTH, height: IDENTITY_PLATE_HEIGHT },
    'SQUARE' => { width: IDENTITY_PLATE_WIDTH, height: IDENTITY_PLATE_HEIGHT }
  }.freeze

  BACKGROUND_URLS = {
    31398 => "https://a-cdn.thecircleapp.in/production/video-posters/background-photos/BJP.png",
    31403 => "https://a-cdn.thecircleapp.in/production/video-posters/background-photos/YSR.png",
    31401 => "https://a-cdn.thecircleapp.in/production/video-posters/background-photos/INC2.png",
    31402 => "https://a-cdn.thecircleapp.in/production/video-posters/background-photos/TDP.png",
    31406 => "https://a-cdn.thecircleapp.in/production/video-posters/background-photos/JSP.png",
    31405 => "https://a-cdn.thecircleapp.in/production/video-posters/background-photos/BRS.png",
    0 => "https://a-cdn.thecircleapp.in/production/video-posters/background-photos/Neutral.png"
  }.freeze

  IDENTITY_BADGE_FONT_SIZE = 18;

  def get_video_mode
    video_frame.video_type
  end

  def get_frame_dimensions(mode)
    dimensions = FRAME_DIMENSIONS[mode]
    raise ArgumentError, "Invalid video mode: #{mode}" unless dimensions
    dimensions
  end

  def get_video_creative_elements(source_video:, gradient_circle_id:)
    mode = get_video_mode
    frame_dimensions = get_frame_dimensions(mode)
    video_dimensions = get_video_dimensions(mode)
    user_photo_dimensions = get_user_photo_dimensions(mode)

    video_creative_elements = if mode == "PORTRAIT"
                                [
                                  get_user_photo_data(user_photo_dimensions, frame_dimensions),
                                  get_identity_data(frame_dimensions)
                                ]
                              elsif mode == "LANDSCAPE" || mode == "SQUARE"
                                [
                                  get_identity_data(frame_dimensions),
                                  get_user_photo_data(user_photo_dimensions, frame_dimensions)
                                ]
                              end

    elements = []
    if mode != 'PORTRAIT'
      elements << get_background_gradient_data(frame_dimensions, gradient_circle_id)
    end
    elements << get_video_data(source_video, video_dimensions, frame_dimensions)
    elements << get_protocol_data
    elements += video_creative_elements

    elements
  end

  private

  def fetch_background_gradient_url(gradient_circle_id)
    BACKGROUND_URLS[gradient_circle_id] || BACKGROUND_URLS[0]
  end

  # get background gradients image
  def get_background_gradient_data(frame_dimensions, gradient_circle_id)

    {
      "url": fetch_background_gradient_url(gradient_circle_id),
      "width": frame_dimensions[:width],
      "height": frame_dimensions[:height],
      "x": 0,
      "y": 0,
      "type": "photo",
      "is_user_photo": false
    }
  end

  def get_protocol_data
    {
      "url": get_protocl_photo_url,
      "width": PROTOCOL_IMAGE_WIDTH,
      "height": PROTOCOL_IMAGE_HEIGHT,
      "x": 0,
      "y": 0,
      "type": "photo",
      "is_user_photo": false
    }
  end

  def get_video_data(source_video, video_dimensions, frame_dimensions)
    mode = get_video_mode

    # Calculate video position based on mode
    x_position, y_position = calculate_video_position(mode, video_dimensions, frame_dimensions)

    video_url = source_video&.source_url || source_video&.url
    radius = (mode != 'PORTRAIT') ? 20 : 0

    {
      "url": video_url,
      "width": video_dimensions[:width],
      "height": video_dimensions[:height],
      "x": x_position,
      "y": y_position,
      "type": "video",
      "is_user_photo": false,
      "border": {
        "radius": radius,
        "width": 0,
        "color":  0x00000000
      }
    }
  end

  def get_user_photo_data(user_photo_dimensions, frame_dimensions)
    mode = get_video_mode

    # Calculate user photo position based on mode
    x_position, y_position = calculate_user_photo_position(mode, user_photo_dimensions, frame_dimensions)

    user_photo_url = user&.video_poster_default_photo&.url

    {
      "url": user_photo_url,
      "width": user_photo_dimensions[:width],
      "height": user_photo_dimensions[:height],
      "x": x_position,
      "y": y_position,
      "type": "photo",
      "is_user_photo": true
    }
  end

  def get_identity_data(frame_dimensions)
    identity_url = self.identity_photo_url

    {
      "url": identity_url,
      "width": IDENTITY_PLATE_WIDTH,
      "height": IDENTITY_PLATE_HEIGHT,
      "x": 0,
      "y": frame_dimensions[:height] - IDENTITY_PLATE_HEIGHT,
      "type": "photo",
      "is_user_photo": false
    }
  end


  def get_video_dimensions(mode)
    dimensions = VIDEO_DIMENSIONS[mode]
    raise ArgumentError, "Invalid video mode: #{mode}" unless dimensions
    dimensions
  end

  def get_user_photo_dimensions(mode)
    dimensions = USER_PHOTO_DIMENSIONS[mode]
    raise ArgumentError, "Invalid video mode: #{mode}" unless dimensions
    dimensions
  end

  # Position calculation methods
  def calculate_video_position(mode, video_dimensions, frame_dimensions)
    case mode
    when 'PORTRAIT'
      x = 0
      y = 0
    when 'SQUARE'
      x = frame_dimensions[:width] - video_dimensions[:width] - VIDEO_PADDING
      y = frame_dimensions[:height] - video_dimensions[:height] - IDENTITY_PLATE_HEIGHT - VIDEO_PADDING
    when 'LANDSCAPE'
      x = frame_dimensions[:width] - video_dimensions[:width] - VIDEO_PADDING
      y = frame_dimensions[:height] - video_dimensions[:height] - IDENTITY_PLATE_HEIGHT - 9
    else
      raise ArgumentError, "Invalid video mode: #{mode}"
    end
    [x, y]
  end

  def calculate_user_photo_position(mode, user_photo_dimensions, frame_dimensions)
    case mode
    when 'PORTRAIT'
      x = 0
      y = frame_dimensions[:height] - IDENTITY_PLATE_HEIGHT - user_photo_dimensions[:height]
    when 'SQUARE', 'LANDSCAPE'
      x = 0
      y = frame_dimensions[:height] - user_photo_dimensions[:height]
    else
      raise ArgumentError, "Invalid video mode: #{mode}"
    end
    [x, y]
  end

  def get_protocl_photo_url
    UserPosterLayout.where(entity: user).last&.video_frame_protocol_photo_url
  end

end
